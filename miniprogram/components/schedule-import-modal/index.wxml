<!-- 工作安排导入模态框 -->
<view class="modal {{modalVisible ? 'show' : ''}}" wx:if="{{visible}}" bind:tap="onClose">
  <view class="modal-content import-modal-content" catch:tap="onStopPropagation">
    <view class="modal-header">
      <view class="modal-title">导入工作安排</view>
      <view class="modal-close" bind:tap="onClose">×</view>
    </view>
    
    <view class="modal-body">
      <!-- 使用提示 -->
      <view class="import-tip">
        <text class="tip-icon">💡</text>
        <text class="tip-text">选择要导入的日期，往下滚动可查看详情</text>
      </view>
      
      <!-- 日历 -->
      <view class="import-calendar">
        <view class="import-calendar-header">
          <view class="import-calendar-nav" bind:tap="onPreviousYear">
            <text class="nav-arrow">‹‹</text>
          </view>
          <view class="import-calendar-nav" bind:tap="onPreviousMonth">
            <text class="nav-arrow">‹</text>
          </view>
          <view class="import-calendar-title">
            <text>{{calendarYear}}年{{calendarMonth}}月</text>
          </view>
          <view class="import-calendar-nav" bind:tap="onNextMonth">
            <text class="nav-arrow">›</text>
          </view>
          <view class="import-calendar-nav" bind:tap="onNextYear">
            <text class="nav-arrow">››</text>
          </view>
        </view>

        <view class="import-calendar-weekdays">
          <view class="weekday">日</view>
          <view class="weekday">一</view>
          <view class="weekday">二</view>
          <view class="weekday">三</view>
          <view class="weekday">四</view>
          <view class="weekday">五</view>
          <view class="weekday">六</view>
        </view>

        <view class="import-calendar-days">
          <block wx:for="{{calendarDays}}" wx:key="index">
            <view class="import-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.date === selectedDate ? 'selected' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}} {{item && item.status ? 'day-status-' + item.status : ''}}"
                  bind:tap="onDateTap"
                  data-index="{{index}}"
                  wx:if="{{item}}"
                  style="{{item && item.statusConfig ? '--status-color: ' + item.statusConfig.color + '; --status-bg-color: ' + item.statusConfig.backgroundColor + '; --status-border-color: ' + item.statusConfig.borderColor + ';' : ''}}">
              <!-- 状态图标 -->
              <view wx:if="{{item.status && item.statusConfig}}" class="status-indicator">
                <text class="status-icon">{{item.statusConfig.icon}}</text>
              </view>

              <text class="day-number">{{item.day}}</text>
              <text wx:if="{{item.holidayName}}" class="holiday-name">{{item.holidayName}}</text>
            </view>
            <view class="import-calendar-day empty" wx:else></view>
          </block>
        </view>
      </view>
      
      <!-- 无数据提示 -->
      <view wx:if="{{!hasAnyData}}" class="empty-state">
        <view class="empty-icon">📅</view>
        <view class="empty-text">当前月份没有工作安排</view>
        <view class="empty-tip">请切换到其他月份或先设置工作安排</view>
      </view>
      
      <!-- 选中日期的详情 -->
      <view wx:if="{{selectedDateData}}" class="date-detail">
        <view class="detail-header">
          <text class="detail-title">{{selectedDate}}</text>
          <view class="detail-status">
            <text class="status-emoji">{{selectedDateData.statusConfig.icon}}</text>
            <text class="status-text">{{selectedDateData.statusConfig.name}}</text>
          </view>
        </view>
        
        <view class="detail-content">
          <view wx:if="{{selectedDateData.dailyIncome > 0}}" class="detail-income">
            <text class="income-label">当日收入：</text>
            <text class="income-value">{{currencySymbol}}{{selectedDateData.dailyIncome}}</text>
          </view>
          
          <view wx:if="{{selectedDateData.segments.length > 0}}" class="detail-segments">
            <text class="segments-label">时间安排：</text>
            <view class="segment-item" wx:for="{{selectedDateData.segments}}" wx:key="index" wx:for-item="segment">
              <text class="segment-type">{{segment.type === 'work' ? '工作' : segment.type === 'rest' ? '休息' : '加班'}}</text>
              <text class="segment-time">{{segment.startTime}} - {{segment.endTime}}</text>
              <text wx:if="{{segment.income > 0}}" class="segment-income">{{currencySymbol}}{{segment.income}}</text>
            </view>
          </view>
          <view wx:else class="detail-segments">
            <text class="segments-label">时间安排：</text>
            <view class="no-segments-placeholder">
              <text class="placeholder-icon">📅</text>
              <text class="placeholder-text">没有添加任何时间安排</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <view class="btn-secondary modal-btn" bind:tap="onCancel">
        <text>取消</text>
      </view>
      <view class="btn-primary modal-btn {{selectedDate ? '' : 'disabled'}}" bind:tap="onConfirm">
        <text>确定导入</text>
      </view>
    </view>
  </view>
</view>
