/* 工作安排导入模态框组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;

  /* 使用统一的transition动画 */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #fff;
  border-radius: 36rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
}

.import-modal-content {
  width: 90%;
  max-width: 800rpx;
  max-height: 90vh; /* 增加高度 */
  overflow-y: auto;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1rpx solid #E5E7EB;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:active {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transform: scale(0.95);
}

/* 模态框主体 */
.modal-body {
  padding: 20rpx 40rpx;
  max-height: 50vh;
  overflow-y: auto;
}

/* 使用提示 */
.import-tip {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 40rpx;
  padding: 24rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  border-left: 8rpx solid #007aff;
}

.tip-icon {
  font-size: 32rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 导入日历样式 */
.import-calendar {
  margin: 20rpx 0;
}

.import-calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}

.import-calendar-nav {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f9fa;
}

.import-calendar-nav:active {
  background-color: #e9ecef;
}

.nav-arrow {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

.import-calendar-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #212529;
}

.import-calendar-weekdays {
  display: flex;
  margin-bottom: 10rpx;
}

.import-calendar-weekdays .weekday {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #6c757d;
  padding: 10rpx 0;
}

.import-calendar-days {
  display: flex;
  flex-wrap: wrap;
  gap: 0; /* 移除间距 */
}

.import-calendar-day {
  width: 14.28%;
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  padding: 4rpx;
  box-sizing: border-box;
  /* 移除边框和圆角 */
}

.import-calendar-day.current-month {
  cursor: pointer;
}

/* 导入日历状态样式 - 与批量日历保持一致 */
/* 工作/出勤状态类 - 蓝色系 */
.import-calendar-day.day-status-work,
.import-calendar-day.day-status-rest,
.import-calendar-day.day-status-rotation_rest,
.import-calendar-day.day-status-compensatory_rest,
.import-calendar-day.day-status-work_suspension,
.import-calendar-day.day-status-standby,
.import-calendar-day.day-status-duty {
  background: var(--status-bg-color, #EFF6FF);
}

.import-calendar-day.day-status-work .day-number,
.import-calendar-day.day-status-rest .day-number,
.import-calendar-day.day-status-rotation_rest .day-number,
.import-calendar-day.day-status-compensatory_rest .day-number,
.import-calendar-day.day-status-work_suspension .day-number,
.import-calendar-day.day-status-standby .day-number,
.import-calendar-day.day-status-duty .day-number {
  color: var(--status-color, #3B82F6);
}

/* 节假日类 - 绿色系 */
.import-calendar-day.day-status-holiday,
.import-calendar-day.day-status-annual_leave,
.import-calendar-day.day-status-festival_leave {
  background: var(--status-bg-color, #ECFDF5);
}

.import-calendar-day.day-status-holiday .day-number,
.import-calendar-day.day-status-annual_leave .day-number,
.import-calendar-day.day-status-festival_leave .day-number {
  color: var(--status-color, #10B981);
}

/* 请假/缺勤类 - 橙色系 */
.import-calendar-day.day-status-leave,
.import-calendar-day.day-status-sick,
.import-calendar-day.day-status-marriage_leave,
.import-calendar-day.day-status-maternity_leave,
.import-calendar-day.day-status-paternity_leave,
.import-calendar-day.day-status-bereavement_leave,
.import-calendar-day.day-status-work_injury_leave,
.import-calendar-day.day-status-family_visit_leave,
.import-calendar-day.day-status-absent {
  background: var(--status-bg-color, #FFFBEB);
}

.import-calendar-day.day-status-leave .day-number,
.import-calendar-day.day-status-sick .day-number,
.import-calendar-day.day-status-marriage_leave .day-number,
.import-calendar-day.day-status-maternity_leave .day-number,
.import-calendar-day.day-status-paternity_leave .day-number,
.import-calendar-day.day-status-bereavement_leave .day-number,
.import-calendar-day.day-status-work_injury_leave .day-number,
.import-calendar-day.day-status-family_visit_leave .day-number,
.import-calendar-day.day-status-absent .day-number {
  color: var(--status-color, #F59E0B);
}

/* 选中状态 */
.import-calendar-day.selected {
  background-color: #C8E6C9 !important;
  border: 2rpx solid #4CAF50 !important;
  box-sizing: border-box !important;
}

.import-calendar-day.selected .day-number {
  font-weight: 700 !important;
  color: #2E7D32 !important;
}

.import-calendar-day.selected .holiday-name {
  color: #2E7D32 !important;
}

.import-calendar-day .day-number {
  font-size: 26rpx;
  color: #212529;
  font-weight: 500;
  z-index: 1;
}

.import-calendar-day .holiday-name {
  font-size: 18rpx;
  color: #6B7280;
  text-align: center;
  line-height: 1;
  margin-top: 2rpx;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 导入日历中不在任职范围内的日期样式 */
.import-calendar-day.day-outside-employment .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

.import-calendar-day.day-outside-employment .holiday-name {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 确保导入日历中不在任职范围内的日期在各种状态下都保持灰色 */
.import-calendar-day.day-outside-employment.has-data .day-number,
.import-calendar-day.day-outside-employment.selected .day-number {
  color: #9ca3af !important;
  opacity: 0.6;
}

.import-calendar-day.day-outside-employment.has-data .holiday-name,
.import-calendar-day.day-outside-employment.selected .holiday-name {
  color: #9ca3af !important;
  opacity: 0.6;
}

/* 导入日历状态图标 */
.import-calendar-day .status-indicator {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: 20rpx;
  height: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.import-calendar-day .status-indicator .status-icon {
  font-size: 16rpx;
  line-height: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #9CA3AF;
}

.empty-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  color: #6B7280;
}

.empty-tip {
  font-size: 28rpx;
  color: #9CA3AF;
}

/* 日期详情 */
.date-detail {
  margin-top: 36rpx;
  padding: 20rpx;
  border: 1rpx solid #dcdcdc;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 24rpx;
}

.detail-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.detail-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #212529;
  text-align: center;
  margin-bottom: 8rpx;
}

.detail-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
  padding: 4rpx 8rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

.status-emoji {
  font-size: 16rpx;
}

.status-text {
  font-size: 20rpx;
  color: #3B82F6;
  font-weight: 500;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.detail-income {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.income-label {
  font-size: 26rpx;
  color: #6B7280;
}

.income-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #059669;
}

.detail-segments {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.segments-label {
  font-size: 26rpx;
  color: #6B7280;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.segment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.segment-type {
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  background-color: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
  font-weight: 500;
}

.segment-time {
  font-size: 24rpx;
  color: #374151;
  flex: 1;
  margin-left: 12rpx;
}

.segment-income {
  font-size: 24rpx;
  color: #059669;
  font-weight: 600;
}

.no-segments-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  border: 1rpx dashed rgba(0, 0, 0, 0.2);
  color: #9CA3AF;
}

.placeholder-icon {
  font-size: 32rpx;
}

.placeholder-text {
  font-size: 28rpx;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 40rpx;
  border-top: 1rpx solid #E5E7EB;
  background: #F9FAFB;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
}

.btn-secondary {
  background: #F3F4F6;
  color: #6B7280;
}

.btn-secondary:active {
  background: #E5E7EB;
  color: #374151;
  transform: scale(0.98);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
}

.btn-primary.disabled {
  background: #E5E7EB;
  color: #9CA3AF;
  box-shadow: none;
  cursor: not-allowed;
}

.btn-primary.disabled:active {
  transform: none;
  box-shadow: none;
}
