// 日历页面逻辑 - 重构版
const { formatDate, formatDateKey, formatTime, formatDuration, getDateStart, isSameDay, calculateHourlyRate, minutesToTimeDisplay, timeStringToMinutes, getWeekdayText, formatDateInfo } = require('../../utils/helpers/time-utils.js')
const { CurrencyUtils } = require('../../utils/currency-utils.js')

Page({
  data: {
    // 当前显示的年月
    currentYear: 2024,
    currentMonth: 1,
    
    // 日历数据
    calendarDays: [],
    weekdays: ['日', '一', '二', '三', '四', '五', '六'],
    
    // 选中的日期和数据
    selectedDate: null,
    selectedDateText: '',
    selectedDateKey: '',
    selectedDayData: null,
    
    // 工作履历相关
    currentWork: null,
    currentWorkId: null,
    currentWorkDisplayName: '',
    workStatus: 'unknown', // 工作状态：pending(待入职)、active(在职)、inactive(已离职)
    workStatusIcon: '💼',
    workStatusColor: '#666666',
    workDaysInfo: [], // 工作天数信息数组
    
    // 时间段显示数据
    displaySegments: [],

    // 摸鱼显示数据
    displayFishes: [],

    // 时间图表数据
    chartSegments: [],
    
    // 有数据的日期
    datesWithData: [],
    
    // 今天
    today: null,
    
    // 模态框状态
    showScheduleModal: false,
    showDateTypeSelector: false,
    showTimeRangePicker: false,
    showIncomeAdjustmentModal: false,

    // 模态框动画状态
    scheduleModalVisible: false,
    importModalVisible: false,
    dailyIncomeCalculatorModalVisible: false,

    // 时间段选择器相关
    editingTimeIndex: -1, // 正在编辑的时间段索引

    // 收入调整相关
    adjustmentModalMode: 'income', // 'income' 或 'deduction'
    adjustmentModalEditItemId: '', // 编辑项目的ID
    adjustmentModalDateString: '', // 传递给模态框的日期字符串
    selectedDayAdjustmentSummary: null,

    // 日期状态相关
    dateStatus: 'work',
    statusOptions: [],
    statusIndex: 0,
    selectedStatusConfig: null,

    // 批量操作相关
    showBatchModal: false,
    batchOperation: 'copy', // copy, import

    // 日收入计算器相关
    showDailyIncomeCalculatorModal: false,
    dailyIncomeCalculatorWorkDays: 21.75, // 默认工作天数
    dailyIncomeCalculatorMonthlyIncome: 10000, // 默认月收入
    dailyIncomeCalculatorTargetMode: 'total', // 目标模式：'total' 或 'overtime'


    
    // 统计信息
    todayIncome: '0.00',
    monthIncome: '0.00',

    // 时间统计
    workTime: '0小时',
    restTime: '0小时',
    overtimeTime: '0小时',

    // 摸鱼统计
    fishingStats: {
      count: 0,
      duration: '0分钟',
      income: '0.00'
    },

    // 货币设置
    currencySymbol: '¥',

    // 引导界面
    hasWorkHistory: true,

    // 节假日相关
    isHolidayLoading: false,

    // 摸鱼编辑器
    showFishingEditor: false,
    fishingEditorMode: 'add', // add, edit
    editingFishing: null,

    // 清空按钮显示控制
    shouldShowClearButton: false
  },

  /**
   * 生成动态样式配置
   */
  generateDynamicStyles() {
    try {
      const categories = this.timeSegmentService.getDateStatusCategories()
      const statusStyleMap = {}

      categories.forEach(category => {
        const colorConfig = this.timeSegmentService.getCategoryColorConfig(category.id)

        category.types.forEach(type => {
          statusStyleMap[type.value] = {
            bgColor: colorConfig.bgColor || '#F3F4F6',
            textColor: colorConfig.color || '#374151'
          }
        })
      })

      // 将样式配置存储到 data 中，供模板使用
      this.setData({
        statusStyleMap: statusStyleMap
      })

      console.log('动态样式配置生成完成:', statusStyleMap)
    } catch (error) {
      console.error('生成动态样式配置失败:', error)
    }
  },

  /**
   * 页面加载时
   */
  onLoad(options) {
    console.log('日历页面加载开始')

    // 获取全局应用实例和服务
    const app = getApp()
    this.dataManager = app.getDataManager()
    this.holidayManager = app.getHolidayManager()
    this.timeSegmentService = app.getTimeSegmentService()
    this.workHistoryService = app.getWorkHistoryService()
    this.incomeAdjustmentService = app.getIncomeAdjustmentService()

    // 加载货币设置
    this.loadCurrencySettings()

    // 生成动态样式
    this.generateDynamicStyles()

    // 设置当前日期
    const now = new Date()
    const selectedDateValue = getDateStart(now)
    console.log('页面初始化 - selectedDate:', selectedDateValue, 'type:', typeof selectedDateValue)

    this.setData({
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1,
      today: getDateStart(now),
      selectedDate: selectedDateValue,
      selectedDateText: formatDate(now),
      selectedDateWeekday: getWeekdayText(now),
      selectedDateKey: getDateStart(now).toISOString()
    })

    console.log('页面初始化后 - this.data.selectedDate:', this.data.selectedDate, 'type:', typeof this.data.selectedDate)

    // 加载当前工作履历
    this.loadCurrentWork()

    // 加载状态选项
    this.loadStatusOptions()

    // 加载数据
    this.loadCalendarData()
    this.loadSelectedDateData()
    this.updateStatistics()
    
    // 初始化节假日数据
    this.initializeHolidayData()
    
    // 注册数据变化监听器
    this.registerDataChangeListener()
    
    console.log('日历页面加载完成')
  },

  /**
   * 页面显示时
   */
  onShow() {
    console.log('日历页面显示')
    
    // 刷新数据
    this.loadCurrentWork()
    this.loadCalendarData()
    this.loadSelectedDateData()
    this.updateStatistics()
  },

  /**
   * 页面卸载时
   */
  onUnload() {
    console.log('日历页面卸载')
    
    // 移除数据变化监听器
    this.unregisterDataChangeListener()
  },

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener() {
    this.dataChangeListener = (userData) => {
      console.log('收到数据变化通知，刷新日历页面数据')
      this.loadCurrentWork()
      this.loadCalendarData()
      this.loadSelectedDateData()
      this.updateStatistics()
    }
    
    this.dataManager.addChangeListener(this.dataChangeListener)
  },

  /**
   * 移除数据变化监听器
   */
  unregisterDataChangeListener() {
    if (this.dataChangeListener) {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }
  },

  /**
   * 加载货币设置
   */
  loadCurrencySettings() {
    try {
      CurrencyUtils.loadCurrencySettingsForContext(this, {
        includeSymbol: true
      })
    } catch (error) {
      console.error('加载货币设置失败:', error)
      this.setData({
        currencySymbol: '¥'
      })
    }
  },

  /**
   * 加载当前工作履历
   */
  loadCurrentWork() {
    try {
      // 检查是否有工作履历
      const hasWorkHistory = this.workHistoryService.hasWorkHistory()
      
      if (!hasWorkHistory) {
        // 没有工作履历，显示引导界面
        this.setData({
          hasWorkHistory: false,
          currentWork: null,
          currentWorkId: null,
          currentWorkDisplayName: '无工作履历',
          workStatus: 'unknown',
          workStatusIcon: '💼',
          workStatusColor: '#666666',
          workDaysInfo: []
        })
        return
      }

      // 有工作履历，加载当前工作
      const currentWork = this.workHistoryService.ensureCurrentWork()
      const displayName = this.workHistoryService.getWorkDisplayName(currentWork)

      // 计算工作状态和天数信息
      const workStatus = this.calculateWorkStatus(currentWork)
      const { icon, color } = this.getWorkStatusDisplay(workStatus)
      const workDaysInfo = this.calculateWorkDaysInfo(currentWork)

      this.setData({
        hasWorkHistory: true,
        currentWork,
        currentWorkId: currentWork ? currentWork.id : null,
        currentWorkDisplayName: displayName,
        workStatus,
        workStatusIcon: icon,
        workStatusColor: color,
        workDaysInfo
      })
      
      console.log('当前工作履历:', displayName)
    } catch (error) {
      console.error('加载当前工作履历失败:', error)
      wx.showToast({
        title: '加载工作履历失败',
        icon: 'none'
      })
    }
  },

  /**
   * 计算工作状态
   * @param {Object} work - 工作履历对象
   * @returns {string} 工作状态：pending(待入职)、active(在职)、inactive(已离职)
   */
  calculateWorkStatus(work) {
    if (!work || !work.startDate) {
      return 'unknown'
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const startDate = new Date(work.startDate)
    startDate.setHours(0, 0, 0, 0)

    // 待入职：开始日期在今天之后
    if (startDate > today) {
      return 'pending'
    }

    // 已离职：有离职日期且离职日期在今天之前
    if (work.endDate) {
      const endDate = new Date(work.endDate)
      endDate.setHours(0, 0, 0, 0)
      if (endDate < today) {
        return 'inactive'
      }
    }

    // 在职：开始日期在今天或之前，且没有离职或离职日期在今天或之后
    return 'active'
  },

  /**
   * 获取工作状态显示信息
   * @param {string} status - 工作状态
   * @returns {Object} 包含图标和颜色的对象
   */
  getWorkStatusDisplay(status) {
    const statusMap = {
      pending: { icon: '⏳', color: '#3B82F6' }, // 蓝色
      active: { icon: '💼', color: '#10B981' },  // 绿色
      inactive: { icon: '💼', color: '#F59E0B' }, // 橙色
      unknown: { icon: '📋', color: '#666666' }   // 灰色
    }

    return statusMap[status] || statusMap.unknown
  },

  /**
   * 计算工作天数信息
   * @param {Object} work - 工作履历对象
   * @returns {Array} 天数信息数组
   */
  calculateWorkDaysInfo(work) {
    if (!work || !work.startDate) {
      return []
    }

    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const startDate = new Date(work.startDate)
    startDate.setHours(0, 0, 0, 0)

    const status = this.calculateWorkStatus(work)
    const daysInfo = []

    if (status === 'pending') {
      // 待入职：显示"xx天后入职"
      const daysToStart = Math.ceil((startDate - today) / (1000 * 60 * 60 * 24))
      daysInfo.push({
        text: `${daysToStart} 天后入职`,
        color: '#3B82F6' // 蓝色
      })
    } else if (status === 'active') {
      // 在职：显示"在职xx天"
      const workDays = Math.ceil((today - startDate) / (1000 * 60 * 60 * 24))
      daysInfo.push({
        text: `在职 ${workDays} 天`,
        color: '#10B981' // 绿色
      })

      // 如果有离职日期，还要显示"xx天后离职"
      if (work.endDate) {
        const endDate = new Date(work.endDate)
        endDate.setHours(0, 0, 0, 0)
        if (endDate >= today) {
          const daysToEnd = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24))
          daysInfo.push({
            text: `${daysToEnd} 天后离职`,
            color: '#F59E0B' // 橙色
          })
        }
      }
    } else if (status === 'inactive') {
      // 已离职：显示"离职xx天"
      if (work.endDate) {
        const endDate = new Date(work.endDate)
        endDate.setHours(0, 0, 0, 0)
        const daysSinceEnd = Math.ceil((today - endDate) / (1000 * 60 * 60 * 24))
        daysInfo.push({
          text: `离职 ${daysSinceEnd} 天`,
          color: '#EF4444' // 红色
        })
      }
    }

    return daysInfo
  },

  /**
   * 跳转到工作履历页面
   */
  goToWorkHistory() {
    wx.switchTab({
      url: '/pages/work-history/index'
    })
  },

  /**
   * 加载日历数据
   */
  loadCalendarData() {
    this.loadDatesWithData()
    this.generateCalendar()
  },

  /**
   * 加载有数据的日期
   */
  loadDatesWithData() {
    const dates = this.timeSegmentService.getDatesWithData()
    this.setData({
      datesWithData: dates
    })
    console.log(`加载了${dates.length}个有数据的日期`)
  },

  /**
   * 加载状态选项
   */
  loadStatusOptions() {
    const statusOptions = this.timeSegmentService.getStatusOptions()
    this.setData({
      statusOptions,
      selectedStatusConfig: statusOptions[0] || null
    })
    console.log(`加载了${statusOptions.length}个状态选项`)
  },

  /**
   * 初始化节假日数据
   */
  async initializeHolidayData() {
    try {
      this.setData({ isHolidayLoading: true })
      
      // 初始化节假日管理器
      await this.holidayManager.initialize()
      
      // 重新生成日历以包含节假日信息
      this.generateCalendar()
      
      // 调试：打印节假日管理器状态
      this.holidayManager.debugStatus()
      
      console.log('节假日数据初始化完成')
    } catch (error) {
      console.error('初始化节假日数据失败:', error)
      wx.showToast({
        title: '节假日数据加载失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      this.setData({ isHolidayLoading: false })
    }
  },

  /**
   * 生成日历
   */
  generateCalendar() {
    const { currentYear, currentMonth } = this.data
    
    // 获取当月第一天和最后一天
    const firstDay = new Date(currentYear, currentMonth - 1, 1)
    const lastDay = new Date(currentYear, currentMonth, 0)
    
    // 获取当月第一天是星期几（0-6，0是星期日）
    const firstDayOfWeek = firstDay.getDay()
    
    // 获取当月天数
    const daysInMonth = lastDay.getDate()
    
    // 生成日历数据
    const calendarDays = []
    
    // 添加上个月的空白天数
    for (let i = 0; i < firstDayOfWeek; i++) {
      calendarDays.push(null)
    }
    
    // 添加当月的天数
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, currentMonth - 1, day)
      const hasData = this.hasDataOnDate(date)
      const isToday = isSameDay(date, this.data.today)
      const isSelected = isSameDay(date, this.data.selectedDate)
      
      // 获取日期状态
      const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
      const statusConfig = status ? this.timeSegmentService.getDateStatusConfig(status) : null
      
      // 获取节假日信息
      let holidayInfo = null
      if (this.holidayManager) {
        holidayInfo = this.holidayManager.getDateInfo(date)

        // 调试日志 - 只对特定日期打印
        const dateStr = formatDateKey(date)
        if (dateStr === '2025-01-01' || dateStr === '2025-01-28' || dateStr.includes('2025-02-0')) {
          console.log(`日历生成 ${dateStr} 节假日信息:`, holidayInfo)
        }
      }

      // 检查是否在任职日期范围内
      const isInEmploymentRange = this.isDateInEmploymentRange(date)

      // 检查是否是发薪日
      const isPayDay = this.isPayDay(date)

      calendarDays.push({
        day,
        date,
        hasData,
        isToday,
        isSelected,
        isCurrentMonth: true,
        status,
        statusConfig,
        holidayInfo,
        isInEmploymentRange,
        isPayDay
      })
    }
    
    this.setData({
      calendarDays
    })
    
    console.log(`生成${currentYear}年${currentMonth}月日历，共${calendarDays.length}天`)
  },

  /**
   * 检查指定日期是否有数据
   */
  hasDataOnDate(date) {
    return this.data.datesWithData.some(dataDate =>
      isSameDay(date, dataDate)
    )
  },

  /**
   * 检查指定日期是否是发薪日
   */
  isPayDay(date) {
    try {
      const currentWorkId = this.workHistoryService.getCurrentWorkId()
      if (!currentWorkId) {
        return false
      }

      const payDays = this.workHistoryService.getPayDays(currentWorkId)
      if (!payDays || payDays.length === 0) {
        return false
      }

      // 使用工作履历服务的calculatePayDate方法来计算实际发薪日期
      const monthDate = new Date(date.getFullYear(), date.getMonth(), 1)

      return payDays.some(payDay => {
        const actualPayDate = this.workHistoryService.calculatePayDate(monthDate, payDay.day)
        return actualPayDate.getDate() === date.getDate()
      })
    } catch (error) {
      console.error('检查发薪日失败:', error)
      return false
    }
  },

  /**
   * 检查指定日期是否在当前工作的任职日期范围内
   * @param {Date} date - 要检查的日期
   * @returns {boolean} 是否在任职范围内
   */
  isDateInEmploymentRange(date) {
    const currentWork = this.data.currentWork
    if (!currentWork) {
      return false
    }

    // 将日期标准化为只包含年月日，去除时间部分
    const checkDate = new Date(date)
    checkDate.setHours(0, 0, 0, 0)
    const startDate = new Date(currentWork.startDate)
    startDate.setHours(0, 0, 0, 0)

    // 检查是否在入职日期之后（包括入职当天）
    if (checkDate < startDate) {
      return false
    }

    // 如果有离职日期，检查是否在离职日期之前（包括离职当天）
    if (currentWork.endDate) {
      const endDate = new Date(currentWork.endDate)
      endDate.setHours(0, 0, 0, 0)
      if (checkDate > endDate) {
        return false
      }
    }

    return true
  },

  /**
   * 加载选中日期的数据
   */
  loadSelectedDateData() {
    if (!this.data.selectedDate) return
    
    const dayData = this.timeSegmentService.getDayData(this.data.selectedDate, this.data.currentWorkId)

    // 格式化时间段数据用于显示
    const displaySegments = dayData.segments.map(segment => {
      const income = segment.income || 0
      const hourlyRate = calculateHourlyRate(segment) || 0

      const startTime = minutesToTimeDisplay(segment.start)
      const endTime = minutesToTimeDisplay(segment.end)
      const duration = formatDuration(segment.end - segment.start)

      return Object.assign({}, segment, {
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        typeText: this.getTypeText(segment.type),
        income: income,
        hourlyRate: hourlyRate,
        incomeText: income.toFixed(2),
        hourlyRateText: hourlyRate > 0 ? hourlyRate.toFixed(2) : ''
      })
    })

    // 格式化摸鱼数据用于显示
    const displayFishes = (dayData.fishes || []).map(fish => {
      const startTime = minutesToTimeDisplay(fish.start)
      const endTime = minutesToTimeDisplay(fish.end)
      const duration = formatDuration(fish.end - fish.start)

      // 找到摸鱼时间对应的工作时间段
      const correspondingSegment = dayData.segments.find(segment => {
        return segment.type !== 'rest' &&
               fish.start >= segment.start &&
               fish.end <= segment.end
      })

      // 计算摸鱼时薪和价值
      let hourlyRate = 0
      let fishingValue = 0

      if (correspondingSegment) {
        const segmentDuration = correspondingSegment.end - correspondingSegment.start
        const segmentHours = segmentDuration / 60
        hourlyRate = segmentHours > 0 ? (correspondingSegment.income || 0) / segmentHours : 0

        const fishingMinutes = fish.end - fish.start
        const fishingHours = fishingMinutes / 60
        fishingValue = fishingHours * hourlyRate
      }

      return Object.assign({}, fish, {
        startTime: startTime,
        endTime: endTime,
        duration: duration,
        remark: fish.remark || '',
        hourlyRate: hourlyRate,
        fishingValue: fishingValue,
        hourlyRateText: hourlyRate > 0 ? hourlyRate.toFixed(2) : '0.00',
        fishingValueText: fishingValue > 0 ? fishingValue.toFixed(2) : '0.00'
      })
    })
    
    // 为时间图表组件准备数据，转换为显示格式
    const chartSegments = dayData.segments.map(segment => {
      return Object.assign({}, segment, {
        startTime: minutesToTimeDisplay(segment.start),
        endTime: minutesToTimeDisplay(segment.end)
      })
    })
    
    // 加载选中日期的状态
    const selectedDateStatus = this.timeSegmentService.getDateStatus(this.data.selectedDate, this.data.currentWorkId) || 'work'

    // 计算状态选择器的索引
    const statusIndex = this.data.statusOptions.findIndex(item => item.value === selectedDateStatus)
    const selectedStatusConfig = this.data.statusOptions[statusIndex] || this.data.statusOptions[0]

    // 获取状态文本，确保安全访问
    const selectedDateStatusText = (selectedStatusConfig && selectedStatusConfig.text) ? selectedStatusConfig.text : '工作'
    
    // 添加格式化的收入文本（使用动态计算）
    const dayStats = this.timeSegmentService.dataManager.timeTrackingManager.getDayDataStats(dayData)
    const selectedDayDataWithText = Object.assign({}, dayData, {
      dailyIncomeText: dayStats.dailyIncome.toFixed(2),
      netIncomeText: dayStats.netIncome.toFixed(2),
      totalIncomeText: dayStats.netIncome.toFixed(2)
    })

    // 加载收入调整数据
    const adjustmentSummary = this.loadSelectedDateAdjustmentData()

    this.setData({
      selectedDayData: selectedDayDataWithText,
      displaySegments,
      displayFishes,
      chartSegments,  // 专门为时间图表组件准备的数据
      dateStatus: selectedDateStatus,
      statusIndex: statusIndex >= 0 ? statusIndex : 0,
      selectedStatusConfig,
      selectedDateStatus: selectedDateStatusText,
      averageHourlyRate: dayStats.hourlyRate.toFixed(2),
      selectedDayAdjustmentSummary: adjustmentSummary
    })

    // 更新时间统计
    this.updateTimeStatistics()

    // 更新摸鱼统计
    this.updateFishingStatistics(displayFishes)

    // 获取选中日期的节假日信息
    let selectedDateInfo = { holidayInfo: null }
    if (this.holidayManager && this.data.selectedDate) {
      selectedDateInfo.holidayInfo = this.holidayManager.getDateInfo(this.data.selectedDate)
    }

    // 计算是否应该显示清空按钮
    const shouldShowClearButton = this.hasAnyDataToClean()

    this.setData({
      selectedDateInfo,
      shouldShowClearButton
    })

    console.log(`加载了${formatDate(this.data.selectedDate)}的数据，是否显示清空按钮: ${shouldShowClearButton}`)
  },

  /**
   * 更新统计信息
   */
  updateStatistics() {
    try {
      // 如果没有当前工作，设置默认值
      if (!this.data.currentWorkId) {
        this.setData({
          todayIncome: '0.00',
          monthIncome: '0.00'
        })
        return
      }

      const today = new Date()
      const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1)
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1)

      // 计算今日收入（使用动态计算）
      const todayData = this.timeSegmentService.getDayData(today, this.data.currentWorkId)
      const todayStats = this.timeSegmentService.dataManager.timeTrackingManager.getDayDataStats(todayData)
      const todayIncome = todayStats.netIncome.toFixed(2)

      // 计算本月收入
      const monthlyStats = this.timeSegmentService.calculateIncomeStatistics(
        [currentMonth, nextMonth], 
        this.data.currentWorkId
      )
      const monthIncome = (monthlyStats.totalIncome || 0).toFixed(2)

      this.setData({
        todayIncome,
        monthIncome
      })
      
      console.log(`日历统计更新完成 - 今日: ${this.data.currencySymbol}${todayIncome}, 本月: ${this.data.currencySymbol}${monthIncome}`)
    } catch (error) {
      console.error('更新统计信息失败:', error)
      // 设置默认值
      this.setData({
        todayIncome: '0.00',
        monthIncome: '0.00'
      })
    }
  },

  /**
   * 更新时间统计信息
   */
  updateTimeStatistics() {
    if (!this.data.selectedDayData || this.data.selectedDayData.segments.length === 0) {
      this.setData({
        workTime: '0小时',
        restTime: '0小时',
        overtimeTime: '0小时',
        workIncome: '0.00',
        restPercentage: '0%',
        overtimeIncome: '0.00',
        averageHourlyRate: '0.00'
      })
      return
    }

    let workMinutes = 0
    let restMinutes = 0
    let overtimeMinutes = 0
    let workIncome = 0
    let overtimeIncome = 0

    this.data.selectedDayData.segments.forEach(segment => {
      const duration = segment.end - segment.start
      const segmentIncome = segment.income || 0

      switch (segment.type) {
        case 'work':
          workMinutes += duration
          workIncome += segmentIncome
          break
        case 'rest':
          restMinutes += duration
          break
        case 'overtime':
          overtimeMinutes += duration
          overtimeIncome += segmentIncome
          break
      }
    })

    // 计算休息时间占工作时间的占比
    const totalWorkMinutes = workMinutes + overtimeMinutes
    const restPercentage = totalWorkMinutes > 0 ?
      Math.round((restMinutes / totalWorkMinutes) * 100) + '%' : '0%'

    this.setData({
      workTime: formatDuration(workMinutes),
      restTime: formatDuration(restMinutes),
      overtimeTime: formatDuration(overtimeMinutes),
      workIncome: workIncome.toFixed(2),
      restPercentage: restPercentage,
      overtimeIncome: overtimeIncome.toFixed(2)
    })
  },

  /**
   * 更新摸鱼统计信息
   */
  updateFishingStatistics(displayFishes) {
    if (!displayFishes || displayFishes.length === 0) {
      this.setData({
        fishingStats: {
          count: 0,
          duration: '0分钟',
          income: '0.00'
        }
      })
      return
    }

    // 计算摸鱼次数
    const count = displayFishes.length

    // 计算摸鱼总时长
    let totalMinutes = 0
    let totalIncome = 0

    displayFishes.forEach(fish => {
      // 计算时长（从原始数据）
      const duration = fish.end - fish.start
      totalMinutes += duration

      // 计算收入
      if (fish.fishingValue) {
        totalIncome += fish.fishingValue
      }
    })

    // 格式化时长
    const duration = formatDuration(totalMinutes)

    // 格式化收入
    const income = totalIncome.toFixed(2)

    this.setData({
      fishingStats: {
        count,
        duration,
        income
      }
    })
  },

  /**
   * 检查是否有任何数据需要清空
   * @returns {boolean} 是否应该显示清空按钮
   */
  hasAnyDataToClean() {
    const { selectedDayData } = this.data

    if (!selectedDayData) {
      return false
    }

    // 检查是否有时间段数据
    if (selectedDayData.segments && selectedDayData.segments.length > 0) {
      return true
    }

    // 检查是否有摸鱼数据
    if (selectedDayData.fishes && selectedDayData.fishes.length > 0) {
      return true
    }

    // 检查是否有额外收入
    if (selectedDayData.extraIncomes && selectedDayData.extraIncomes.length > 0) {
      return true
    }

    // 检查是否有扣款
    if (selectedDayData.deductions && selectedDayData.deductions.length > 0) {
      return true
    }

    // 检查是否设置了非默认的日期状态
    if (selectedDayData.status && selectedDayData.status !== 'work') {
      return true
    }

    return false
  },

  /**
   * 获取类型文本
   */
  getTypeText(type) {
    const typeMap = {
      work: '工作',
      rest: '休息',
      overtime: '加班'
    }
    return typeMap[type] || type
  },

  /**
   * 上一个月
   */
  onPreviousMonth() {
    let { currentYear, currentMonth } = this.data
    
    currentMonth--
    if (currentMonth < 1) {
      currentMonth = 12
      currentYear--
    }
    
    this.setData({
      currentYear,
      currentMonth
    })
    
    this.generateCalendar()
  },

  /**
   * 下一个月
   */
  onNextMonth() {
    let { currentYear, currentMonth } = this.data
    
    currentMonth++
    if (currentMonth > 12) {
      currentMonth = 1
      currentYear++
    }
    
    this.setData({
      currentYear,
      currentMonth
    })
    
    this.generateCalendar()
  },

  /**
   * 上一年
   */
  onPreviousYear() {
    let { currentYear } = this.data
    currentYear--
    this.setData({
      currentYear
    })
    this.generateCalendar()
  },

  /**
   * 下一年
   */
  onNextYear() {
    let { currentYear } = this.data
    currentYear++
    this.setData({
      currentYear
    })
    this.generateCalendar()
  },

  /**
   * 回到今天
   */
  onToday() {
    const now = new Date()

    this.setData({
      currentYear: now.getFullYear(),
      currentMonth: now.getMonth() + 1,
      selectedDate: getDateStart(now),
      selectedDateText: formatDate(now),
      selectedDateWeekday: getWeekdayText(now),
      selectedDateKey: getDateStart(now).toISOString()
    })
    
    this.generateCalendar()
    this.loadSelectedDateData()
  },

  /**
   * 日期点击事件
   */
  onDateTap(e) {
    const { day } = e.currentTarget.dataset
    
    if (!day) return
    
    const { currentYear, currentMonth } = this.data
    const selectedDate = new Date(currentYear, currentMonth - 1, day)

    this.setData({
      selectedDate: getDateStart(selectedDate),
      selectedDateText: formatDate(selectedDate),
      selectedDateWeekday: getWeekdayText(selectedDate),
      selectedDateKey: getDateStart(selectedDate).toISOString()
    })
    
    this.generateCalendar()
    this.loadSelectedDateData()
    
    console.log('选中日期:', formatDate(selectedDate))
  },

  /**
   * 设置工作计划
   */
  onSetSchedule() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }

    // 打开设置工作计划模态框
    this.setData({
      showScheduleModal: true
    })
  },

  /**
   * 关闭设置模态框
   */
  onCloseScheduleModal() {
    this.setData({
      showScheduleModal: false
    })
  },

  /**
   * 阻止事件冒泡
   */
  onStopPropagation() {
    // 阻止点击模态框内容时关闭模态框
  },

  /**
   * 打开日期类型选择器
   */
  onOpenDateTypeSelector() {
    console.log('日历页面 - 打开日期类型选择器')
    this.setData({
      showDateTypeSelector: true
    })
  },

  /**
   * 日期类型选择器 - 选择类型
   */
  onDateTypeSelectorSelect(e) {
    const { value } = e.detail
    console.log('日历页面 - 日期类型选择器选择:', value)

    // 更新当前选中的状态
    const statusConfig = this.timeSegmentService.getDateStatusConfig(value)

    this.setData({
      dateStatus: value,
      selectedStatusConfig: statusConfig
    })
  },

  /**
   * 日期类型选择器 - 确认选择
   */
  onDateTypeSelectorConfirm(e) {
    const { value } = e.detail
    console.log('日历页面 - 日期类型选择器确认:', value)

    // 更新状态
    const statusConfig = this.timeSegmentService.getDateStatusConfig(value)
    const statusIndex = this.data.statusOptions.findIndex(option => option.value === value)

    this.setData({
      dateStatus: value,
      selectedStatusConfig: statusConfig,
      showDateTypeSelector: false
    })

    // 如果设置工作计划模态框是打开的，回调结果到组件
    if (this.data.showScheduleModal) {
      const scheduleComponent = this.getScheduleModalComponent()
      console.log('页面：回调日期状态到组件', {
        value,
        statusIndex,
        finalIndex: statusIndex >= 0 ? statusIndex : 0,
        scheduleComponent: !!scheduleComponent
      })

      if (scheduleComponent) {
        scheduleComponent.onDateTypeSelected(statusIndex >= 0 ? statusIndex : 0)
      } else {
        console.warn('页面：无法获取设置工作计划组件实例')
      }
    }
  },

  /**
   * 日期类型选择器 - 取消选择
   */
  onDateTypeSelectorCancel() {
    console.log('日历页面 - 日期类型选择器取消')
    this.setData({
      showDateTypeSelector: false
    })
  },

  /**
   * 日期类型选择器 - 关闭
   */
  onDateTypeSelectorClose() {
    console.log('日历页面 - 日期类型选择器关闭')
    this.setData({
      showDateTypeSelector: false
    })
  },

  /**
   * 打开时间段选择器
   */
  onOpenTimeRangePicker(e) {
    const { index } = e.currentTarget.dataset
    const timeInput = this.data.timeInputs[index]

    console.log('日历页面 - 打开时间段选择器:', { index, timeInput })

    this.setData({
      showTimeRangePicker: true,
      editingTimeIndex: index
    })
  },

  /**
   * 时间段选择器 - 确认选择
   */
  onTimeRangePickerConfirm(e) {
    const { startTime, endTime, isStartNextDay, isEndNextDay, duration } = e.detail
    const { editingTimeIndex } = this.data

    console.log('日历页面 - 时间段选择器确认:', { startTime, endTime, isStartNextDay, isEndNextDay, duration, editingTimeIndex })

    this.setData({
      showTimeRangePicker: false,
      editingTimeIndex: -1
    })

    // 如果设置工作计划模态框是打开的，回调结果到组件
    if (this.data.showScheduleModal && editingTimeIndex >= 0) {
      const scheduleComponent = this.getScheduleModalComponent()
      if (scheduleComponent) {
        scheduleComponent.onTimeRangeSelected({
          index: editingTimeIndex,
          startTime,
          endTime,
          isStartNextDay,
          isEndNextDay,
          duration
        })
      } else {
        console.warn('页面：无法获取设置工作计划组件实例')
      }
    } else if (editingTimeIndex >= 0) {
      // 原有逻辑保持不变（用于其他地方的时间选择器）
      const timeInputs = [...this.data.timeInputs]
      timeInputs[editingTimeIndex] = {
        ...timeInputs[editingTimeIndex],
        startTime,
        endTime,
        isStartNextDay,
        isEndNextDay,
        duration // 保存时长显示文本
      }

      this.setData({
        timeInputs
      })

      // 重新计算所有相关数据
      this.updateAllCalculations()
    }
  },

  /**
   * 时间段选择器 - 取消选择
   */
  onTimeRangePickerCancel() {
    console.log('日历页面 - 时间段选择器取消')
    this.setData({
      showTimeRangePicker: false,
      editingTimeIndex: -1
    })
  },

  /**
   * 时间段选择器 - 关闭
   */
  onTimeRangePickerClose() {
    console.log('日历页面 - 时间段选择器关闭')
    this.setData({
      showTimeRangePicker: false,
      editingTimeIndex: -1
    })
  },

  /**
   * 触发全局数据更新事件
   */
  triggerDataUpdateEvent() {
    try {
      // 通过全局事件总线通知其他页面数据已更新
      const app = getApp()
      if (app.globalData.eventBus && app.globalData.eventBus.onDataUpdate) {
        app.globalData.eventBus.onDataUpdate()
      }
    } catch (error) {
      console.error('触发数据更新事件失败:', error)
    }
  },

  /**
   * 清除选中日期的数据
   */
  onClearDay() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }
    
    wx.showModal({
      title: '确认清除',
      content: `确定要清除 ${this.data.selectedDateText} 的所有数据吗？`,
      confirmText: '清除',
      confirmColor: '#EF4444',
      success: (res) => {
        if (res.confirm) {
          try {
            this.timeSegmentService.clearDayData(this.data.selectedDate)
            
            // 刷新数据
            this.loadCalendarData()
            this.loadSelectedDateData()
            this.updateStatistics()
            
            // 触发全局数据更新事件
            this.triggerDataUpdateEvent()
            
            wx.showToast({
              title: '清除成功',
              icon: 'success'
            })
          } catch (error) {
            console.error('清除数据失败:', error)
            wx.showToast({
              title: '清除失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },



  /**
   * 时间加法辅助函数
   */
  addHours(timeStr, hours) {
    const [h, m] = timeStr.split(':').map(Number)
    const newHours = (h + hours) % 24
    return `${newHours.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    return {
      title: '时间跟踪器 - 工作计划',
      path: '/pages/calendar/index'
    }
  },

  /**
   * 调试方法：强制更新节假日数据
   */
  async debugForceUpdateHolidays() {
    try {
      console.log('开始强制更新节假日数据...')
      await this.holidayManager.forceUpdate()
      this.generateCalendar()
      this.loadSelectedDateData()
      wx.showToast({
        title: '节假日数据更新完成',
        icon: 'success'
      })
    } catch (error) {
      console.error('强制更新失败:', error)
      wx.showToast({
        title: '更新失败: ' + error.message,
        icon: 'none'
      })
    }
  },

  // ========== 批量操作相关方法 ==========

  /**
   * 导入工作安排（保留原有功能）
   */
  onImportFromDate() {
    if (!this.data.selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      })
      return
    }

    // 获取所有有数据的日期作为模板
    const templateDates = this.timeSegmentService.getDatesWithData()
      .map(date => {
        const dayData = this.timeSegmentService.getDayData(date)
        const status = this.timeSegmentService.getDateStatus(date, this.data.currentWorkId)
        const statusConfig = this.timeSegmentService.getDateStatusConfig(status)
        
        return {
          date,
          dateKey: date.toISOString(),
          dateText: formatDate(date),
          dailyIncome: dayData.dailyIncome || 0,
          segmentCount: dayData.segments.length,
          statusConfig: {
            ...statusConfig,
            name: statusConfig.name || '无状态'
          }
        }
      })
      .filter(item => item.segmentCount > 0)
    
    this.setData({
      showBatchModal: true,
      batchOperation: 'import',
      templateDates
    })

    // 延迟显示动画
    setTimeout(() => {
      this.setData({ batchModalVisible: true })
    }, 50)
  },

  /**
   * 批量复制
   */
  onBatchCopy() {
    this.setData({
      showBatchModal: true,
      batchOperation: 'copy'
    })
  },

  /**
   * 批量导入
   */
  onBatchImport() {
    this.setData({
      showBatchModal: true,
      batchOperation: 'import'
    })
  },

  /**
   * 批量操作数据更新事件
   */
  onBatchOperationDataUpdated(e) {
    const { operation, targetDate, targetDates, count } = e.detail
    console.log('页面：收到批量操作数据更新通知', { operation, targetDate, targetDates, count })

    // 刷新页面数据
    this.loadCalendarData()
    this.loadSelectedDateData()
    this.updateStatistics()
  },



  /**
   * 批量操作取消事件
   */
  onBatchOperationCancel() {
    // 取消操作，无需特殊处理
  },

  /**
   * 批量操作关闭事件
   */
  onBatchOperationClose() {
    this.setData({
      showBatchModal: false
    })
  },

  /**
   * 设置工作计划数据更新事件
   */
  onScheduleDataUpdated(e) {
    const { selectedDate } = e.detail
    console.log('页面：收到设置工作计划数据更新通知', { selectedDate })

    // 刷新页面数据
    this.loadCalendarData()
    this.loadSelectedDateData()
    this.updateStatistics()
  },

  /**
   * 设置工作计划取消事件
   */
  onScheduleCancel() {
    // 取消操作，无需特殊处理
  },

  /**
   * 设置工作计划关闭事件
   */
  onScheduleClose() {
    this.setData({
      showScheduleModal: false
    })
  },

  /**
   * 复制工作安排到指定日期
   */
  copyScheduleFromDate(sourceDate, targetDate, options = {}) {
    console.log('copyScheduleFromDate 调用:', {
      sourceDate,
      targetDate,
      targetDateType: typeof targetDate,
      targetDateIsArray: Array.isArray(targetDate),
      options
    })

    try {
      // 处理单个日期或日期数组
      const targetDates = Array.isArray(targetDate) ? targetDate : [targetDate]

      console.log('处理后的目标日期:', {
        targetDates,
        targetDatesLength: targetDates.length
      })

      this.timeSegmentService.copyScheduleToOtherDates(
        sourceDate,
        targetDates,
        options.copyStatus !== undefined ? options.copyStatus : this.data.copyStatus,
        options.copyIncome !== undefined ? options.copyIncome : this.data.copyIncome
      )

      // 刷新数据
      this.loadCalendarData()
      this.loadSelectedDateData()
      this.updateStatistics()

      // 触发全局数据更新事件
      this.triggerDataUpdateEvent()

      wx.showToast({
        title: `已复制到${targetDates.length}个日期`,
        icon: 'success'
      })
    } catch (error) {
      console.error('复制失败:', error)
      wx.showToast({
        title: '复制失败',
        icon: 'none'
      })
    }
  },

  /**
   * 时薪输入框失去焦点
   */
  onHourlyRateBlur(e) {
    const { index } = e.currentTarget.dataset
    const timeInputs = this.data.timeInputs.slice()

    if (timeInputs[index]) {
      // 清除编辑标记，允许重新格式化
      timeInputs[index]._isEditingHourlyRate = false
      this.setData({ timeInputs })
    }
  },

  /**
   * 限制小数位数
   */
  limitDecimalPlaces(value, maxDecimalPlaces) {
    if (!value || value === '') return ''

    // 移除非数字和小数点的字符
    value = value.replace(/[^\d.]/g, '')

    // 处理以小数点开头的情况：.123 -> 0.123
    if (value.startsWith('.')) {
      value = '0' + value
    }

    // 确保只有一个小数点
    const parts = value.split('.')
    if (parts.length > 2) {
      // 保留第一个小数点，移除后续的小数点
      value = parts[0] + '.' + parts.slice(1).join('').replace(/\./g, '')
    }

    // 重新分割以获取正确的部分
    const finalParts = value.split('.')

    // 处理前导零的情况
    if (finalParts[0].length > 1 && finalParts[0].startsWith('0')) {
      // 移除前导零，但保留单独的0（如0.123）
      finalParts[0] = finalParts[0].replace(/^0+/, '') || '0'
      value = finalParts.join('.')
    }

    // 限制小数位数
    if (finalParts.length === 2 && finalParts[1].length > maxDecimalPlaces) {
      const integerPart = finalParts[0]
      const decimalPart = finalParts[1].substring(0, maxDecimalPlaces)
      value = integerPart + '.' + decimalPart
    }

    return value
  },

  /**
   * 打开日收入计算器模态框
   */
  onOpenDailyIncomeCalculator() {
    // 获取当前工作的月薪作为默认值
    const currentMonthlySalary = this.getCurrentWorkMonthlySalary()

    this.setData({
      dailyIncomeCalculatorMonthlyIncome: currentMonthlySalary,
      dailyIncomeCalculatorTargetMode: 'total',
      showDailyIncomeCalculatorModal: true
    })
  },

  /**
   * 日收入计算器确认事件
   */
  onDailyIncomeCalculatorConfirm(e) {
    const { result, targetMode } = e.detail

    // 如果智能收入模态框是打开的，通过全局引用回填结果
    if (this.data.showSmartIncomeModal) {
      if (typeof getApp === 'function') {
        const app = getApp()
        const smartIncomeComponent = app.globalData && app.globalData.smartIncomeComponent
        if (smartIncomeComponent) {
          smartIncomeComponent.fillDailyIncomeResult(result, targetMode)
        } else {
          console.error('无法获取智能收入组件实例进行回填')
        }
      }
    }

    wx.showToast({
      title: '日收入已填入',
      icon: 'success'
    })
  },

  /**
   * 日收入计算器取消事件
   */
  onDailyIncomeCalculatorCancel() {
    // 取消操作，无需特殊处理
  },

  /**
   * 日收入计算器关闭事件
   */
  onDailyIncomeCalculatorClose() {
    this.setData({
      showDailyIncomeCalculatorModal: false,
      dailyIncomeCalculatorTargetMode: 'total'
    })
  },

  /**
   * 获取当前工作的月薪
   */
  getCurrentWorkMonthlySalary() {
    try {
      const currentWork = this.workHistoryService.getCurrentWork()
      if (!currentWork) {
        return 10000 // 默认值
      }

      // 获取当前日期
      const currentDate = new Date()

      // 1. 当前工作履历设置了转正薪资
      if (currentWork.formalSalary && currentWork.formalSalary > 0) {
        // 1.1. 没有设置转正日期，直接采用转正薪资
        if (!currentWork.probationEndDate) {
          return currentWork.formalSalary
        }

        // 1.2. 设置了转正日期，判断是否转正
        const probationEndDate = new Date(currentWork.probationEndDate)

        // 1.2.1. 如果当前已经转正，则采用转正薪资
        if (currentDate > probationEndDate) {
          return currentWork.formalSalary
        }

        // 1.2.2. 如果当前没有转正，则采用试用薪资
        if (currentWork.probationSalary && currentWork.probationSalary > 0) {
          return currentWork.probationSalary
        }

        // 如果没有试用薪资，但有转正薪资，使用转正薪资
        return currentWork.formalSalary
      }

      // 2. 当前工作履历没有设置转正薪资，但是设置了试用薪资，则采用试用薪资
      if (currentWork.probationSalary && currentWork.probationSalary > 0) {
        return currentWork.probationSalary
      }

      // 3. 当前工作履历没有设置转正薪资和试用薪资，则采用默认值
      return 10000
    } catch (error) {
      console.error('获取当前工作月薪失败:', error)
      return 10000 // 默认值
    }
  },

  /**
   * 打开日收入计算器模态框（加班倍率模式）
   */
  onOpenDailyIncomeCalculatorForOvertime() {
    // 获取当前工作的月薪作为默认值
    const currentMonthlySalary = this.getCurrentWorkMonthlySalary()

    this.setData({
      dailyIncomeCalculatorMonthlyIncome: currentMonthlySalary,
      dailyIncomeCalculatorTargetMode: 'overtime',
      showDailyIncomeCalculatorModal: true
    })
  },



  /**
   * 按加班倍率计算
   */
  calculateOvertimeRateIncome(timeInputs) {
    const overtimeRate = parseFloat(this.data.smartIncomeOvertimeRate) || 1.5
    const calculationMethod = this.data.smartIncomeOvertimeCalculationMethod

    if (calculationMethod === 'hourly') {
      // 基础时薪方式
      this.calculateOvertimeRateByHourly(timeInputs, overtimeRate)
    } else {
      // 总收入方式
      this.calculateOvertimeRateByTotal(timeInputs, overtimeRate)
    }
  },

  /**
   * 基础时薪方式计算加班倍率
   */
  calculateOvertimeRateByHourly(timeInputs, overtimeRate) {
    const baseHourly = parseFloat(this.data.smartIncomeBaseHourly) || 0

    if (baseHourly <= 0) {
      throw new Error('请输入有效的基础时薪')
    }

    timeInputs.forEach(input => {
      if (input.type === 'rest') {
        input.income = 0
        input.hourlyRate = 0
        input.hourlyRateText = ''
      } else {
        const duration = this.calculateInputDuration(input)
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = parseFloat((baseHourly * overtimeRate).toFixed(2))
        } else {
          input.hourlyRate = parseFloat(baseHourly.toFixed(2))
        }

        input.income = parseFloat((hours * input.hourlyRate).toFixed(2))
        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'hourlyRate' // 标记为基于时薪计算
      }
    })
  },

  /**
   * 总收入方式计算加班倍率
   */
  calculateOvertimeRateByTotal(timeInputs, overtimeRate) {
    const totalAmount = parseFloat(this.data.smartIncomeOvertimeTotalAmount) || 0

    if (totalAmount <= 0) {
      throw new Error('请输入有效的总收入')
    }

    // 计算总工作时间和加班时间
    let totalWorkMinutes = 0
    let totalOvertimeMinutes = 0
    let totalNormalMinutes = 0

    timeInputs.forEach(input => {
      if (input.type !== 'rest') {
        const duration = this.calculateInputDuration(input)
        totalWorkMinutes += duration

        if (input.type === 'overtime') {
          totalOvertimeMinutes += duration
        } else {
          totalNormalMinutes += duration
        }
      }
    })

    if (totalWorkMinutes === 0) {
      throw new Error('没有工作时间段')
    }

    // 根据加班倍率计算基础时薪
    // 设基础时薪为 x，则：
    // 总收入 = 正常工作时间 * x + 加班时间 * x * 倍率
    // 总收入 = x * (正常工作时间 + 加班时间 * 倍率)
    // x = 总收入 / (正常工作时间 + 加班时间 * 倍率)

    const normalHours = totalNormalMinutes / 60
    const overtimeHours = totalOvertimeMinutes / 60
    const weightedTotalHours = normalHours + (overtimeHours * overtimeRate)

    if (weightedTotalHours === 0) {
      throw new Error('计算出的加权工作时间为0')
    }

    const baseHourlyRate = totalAmount / weightedTotalHours

    // 为每个时间段分配收入
    timeInputs.forEach(input => {
      if (input.type === 'rest') {
        input.income = 0
        input.hourlyRate = 0
        input.hourlyRateText = ''
      } else {
        const duration = this.calculateInputDuration(input)
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = parseFloat((baseHourlyRate * overtimeRate).toFixed(2))
        } else {
          input.hourlyRate = parseFloat(baseHourlyRate.toFixed(2))
        }

        input.income = Math.round(hours * input.hourlyRate * 100) / 100
        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'income' // 标记为基于收入计算
      }
    })

    console.log('总收入方式计算结果:', {
      totalAmount,
      normalHours: normalHours.toFixed(2),
      overtimeHours: overtimeHours.toFixed(2),
      overtimeRate,
      weightedTotalHours: weightedTotalHours.toFixed(2),
      baseHourlyRate: baseHourlyRate.toFixed(2)
    })
  },

  /**
   * 按分类时薪计算
   */
  calculateHourlyRateIncome(timeInputs) {
    const workHourly = parseFloat(this.data.smartIncomeWorkHourly) || 0
    const overtimeHourly = parseFloat(this.data.smartIncomeOvertimeHourly) || 0

    if (workHourly <= 0 && overtimeHourly <= 0) {
      throw new Error('请至少输入一种时薪')
    }

    timeInputs.forEach(input => {
      if (input.type === 'rest') {
        input.income = 0
        input.hourlyRate = 0
        input.hourlyRateText = ''
      } else {
        const duration = this.calculateInputDuration(input)
        const hours = duration / 60

        if (input.type === 'overtime') {
          input.hourlyRate = parseFloat(overtimeHourly.toFixed(2))
        } else {
          input.hourlyRate = parseFloat(workHourly.toFixed(2))
        }

        input.income = parseFloat((hours * input.hourlyRate).toFixed(2))
        input.incomeText = input.income.toString()
        input.hourlyRateText = input.hourlyRate.toFixed(2)
        input._lastUpdatedBy = 'hourlyRate' // 标记为基于时薪计算
      }
    })
  },

  /**
   * 计算时间输入的持续时间（分钟）
   */
  calculateInputDuration(input) {
    if (!input.startTime || !input.endTime) return 0

    const [startHour, startMinute] = input.startTime.split(':').map(Number)
    const [endHour, endMinute] = input.endTime.split(':').map(Number)

    let startMinutes = startHour * 60 + startMinute
    let endMinutes = endHour * 60 + endMinute

    // 处理次日标记
    if (input.isStartNextDay) startMinutes += 24 * 60
    if (input.isEndNextDay) endMinutes += 24 * 60

    // 如果结束时间小于等于开始时间，且没有明确的次日标记，返回0（无效）
    if (endMinutes <= startMinutes && !input.isEndNextDay && !input.isStartNextDay) {
      return 0
    }

    return Math.max(0, endMinutes - startMinutes)
  },

  /**
   * 检测时间段问题（包括冲突、无效时间等）
   */
  checkTimeConflicts() {
    const timeInputs = this.data.timeInputs
    let hasAnyIssue = false

    // 重置所有时间段的问题状态
    timeInputs.forEach(input => {
      input.hasConflict = false
      input.warningMessage = ''
      input.warningType = ''
    })

    // 检查每个时间段的有效性
    timeInputs.forEach((input, index) => {
      const issues = this.validateTimeSegment(input, index)
      if (issues.length > 0) {
        input.hasConflict = true
        input.warningMessage = issues[0].message // 显示第一个问题
        input.warningType = issues[0].type
        hasAnyIssue = true
      }
    })

    // 检查时间段重叠
    for (let i = 0; i < timeInputs.length; i++) {
      for (let j = i + 1; j < timeInputs.length; j++) {
        if (this.isTimeRangeOverlap(timeInputs[i], timeInputs[j])) {
          timeInputs[i].hasConflict = true
          timeInputs[j].hasConflict = true
          timeInputs[i].warningMessage = `与第${j + 1}个时间段重叠`
          timeInputs[j].warningMessage = `与第${i + 1}个时间段重叠`
          timeInputs[i].warningType = 'overlap'
          timeInputs[j].warningType = 'overlap'
          hasAnyIssue = true
        }
      }
    }

    this.setData({
      timeInputs: timeInputs.slice(),
      hasTimeConflict: hasAnyIssue
    })

    return hasAnyIssue
  },

  /**
   * 验证单个时间段
   */
  validateTimeSegment(input) {
    const issues = []

    // 检查时间是否填写完整
    if (!input.startTime || !input.endTime) {
      issues.push({
        type: 'incomplete',
        message: '请填写完整的时间'
      })
      return issues
    }

    // 解析时间
    const [startHour, startMinute] = input.startTime.split(':').map(Number)
    const [endHour, endMinute] = input.endTime.split(':').map(Number)

    let startMinutes = startHour * 60 + startMinute
    let endMinutes = endHour * 60 + endMinute

    // 处理次日标记
    if (input.isStartNextDay) startMinutes += 24 * 60
    if (input.isEndNextDay) endMinutes += 24 * 60

    // 检查开始时间是否等于结束时间
    if (startMinutes === endMinutes) {
      issues.push({
        type: 'same_time',
        message: '开始时间和结束时间不能相同'
      })
      return issues
    }

    // 检查结束时间是否早于开始时间（且没有正确标记次日）
    if (endMinutes < startMinutes) {
      // 如果结束时间早于开始时间，但没有标记结束时间为次日，这是错误的
      if (!input.isEndNextDay && !input.isStartNextDay) {
        issues.push({
          type: 'end_before_start',
          message: '结束时间早于开始时间，请勾选"次日"'
        })
        return issues
      }
    }

    // 计算实际持续时间
    const duration = this.calculateInputDuration(input)

    // 检查时间段是否过短（小于1分钟）
    if (duration < 1) {
      issues.push({
        type: 'too_short',
        message: '时间段无效'
      })
    }

    return issues
  },

  /**
   * 检查两个时间段是否重叠
   */
  isTimeRangeOverlap(input1, input2) {
    const start1 = timeStringToMinutes(input1.startTime, input1.isStartNextDay)
    const end1 = timeStringToMinutes(input1.endTime, input1.isEndNextDay)
    const start2 = timeStringToMinutes(input2.startTime, input2.isStartNextDay)
    const end2 = timeStringToMinutes(input2.endTime, input2.isEndNextDay)

    // 处理传统跨日逻辑
    let actualEnd1 = end1
    let actualEnd2 = end2

    if (!input1.isStartNextDay && !input1.isEndNextDay && end1 <= start1) {
      actualEnd1 = end1 + 24 * 60
    }

    if (!input2.isStartNextDay && !input2.isEndNextDay && end2 <= start2) {
      actualEnd2 = end2 + 24 * 60
    }

    // 检查是否有重叠（不包括边界相等的情况）
    return start1 < actualEnd2 && start2 < actualEnd1
  },

  /**
   * 计算总收入
   */
  calculateTotalIncome() {
    const timeInputs = this.data.timeInputs
    let total = 0

    timeInputs.forEach(input => {
      if (input.type !== 'rest' && input.income) {
        total += parseFloat(input.income) || 0
      }
    })

    this.setData({
      totalIncome: total,
      totalIncomeText: total.toFixed(2)
    })

    return total
  },

  /**
   * 计算时间统计
   */
  calculateTimeStatistics() {
    const timeInputs = this.data.timeInputs
    let workMinutes = 0
    let restMinutes = 0
    let overtimeMinutes = 0

    timeInputs.forEach((input) => {
      const duration = this.calculateInputDuration(input)

      if (duration > 0) {
        switch (input.type) {
          case 'work':
            workMinutes += duration
            break
          case 'rest':
            restMinutes += duration
            break
          case 'overtime':
            overtimeMinutes += duration
            break
        }
      }
    })

    // 转换为小时并格式化显示
    const formatHours = (minutes) => {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60

      if (hours > 0 && remainingMinutes > 0) {
        return `${hours}小时${remainingMinutes}分钟`
      } else if (hours > 0) {
        return `${hours}小时`
      } else if (remainingMinutes > 0) {
        return `${remainingMinutes}分钟`
      } else {
        return '0小时'
      }
    }

    this.setData({
      workHours: workMinutes / 60,
      workHoursText: formatHours(workMinutes),
      restHours: restMinutes / 60,
      restHoursText: formatHours(restMinutes),
      overtimeHours: overtimeMinutes / 60,
      overtimeHoursText: formatHours(overtimeMinutes)
    })
  },

  /**
   * 按开始时间排序时间段
   */
  sortTimeInputsByStartTime() {
    const timeInputs = this.data.timeInputs.slice()

    timeInputs.sort((a, b) => {
      const timeA = timeStringToMinutes(a.startTime, a.isStartNextDay)
      const timeB = timeStringToMinutes(b.startTime, b.isStartNextDay)
      return timeA - timeB
    })

    this.setData({
      timeInputs
    })
  },

  /**
   * 计算合理的默认时薪
   */
  calculateReasonableHourlyRate(existingTimeInputs) {
    // 1. 优先使用现有时间段的平均时薪
    const workSegments = existingTimeInputs.filter(input =>
      (input.type === 'work' || input.type === 'overtime') && input.hourlyRate > 0
    )

    if (workSegments.length > 0) {
      const totalHourlyRate = workSegments.reduce((sum, input) => sum + input.hourlyRate, 0)
      return Math.round((totalHourlyRate / workSegments.length) * 100) / 100
    }

    // 2. 如果没有现有时薪，使用日收入目标计算
    const dailyIncome = this.data.dailyIncome || 500
    const estimatedWorkHours = 8 // 假设一天工作8小时
    const estimatedHourlyRate = dailyIncome / estimatedWorkHours

    return Math.round(estimatedHourlyRate * 100) / 100
  },

  /**
   * 更新所有计算（统一方法）
   */
  updateAllCalculations() {
    this.updateTimeInputsDisplay()
    this.calculateTotalIncome()
    this.calculateTimeStatistics()
    this.checkTimeConflicts()
  },

  /**
   * 更新时间段的持续时间和时薪显示
   */
  updateTimeInputsDisplay() {
    const timeInputs = this.data.timeInputs.slice()

    timeInputs.forEach(input => {
      // 计算持续时间
      const duration = this.calculateInputDuration(input)
      const hours = Math.floor(duration / 60)
      const minutes = duration % 60

      if (hours > 0 && minutes > 0) {
        input.durationText = `${hours}小时${minutes}分钟`
      } else if (hours > 0) {
        input.durationText = `${hours}小时`
      } else {
        input.durationText = `${minutes}分钟`
      }

      // 重新计算收入和时薪（基于最后编辑的字段）
      if (input.type !== 'rest' && duration > 0) {
        // 如果有收入和时薪，需要根据最后编辑的字段重新计算
        if (input.income > 0 || input.hourlyRate > 0) {
          // 如果最后编辑的是时薪，或者只有时薪没有收入，则根据时薪计算收入
          if (input._lastUpdatedBy === 'hourlyRate' || (input.hourlyRate > 0 && !input.income)) {
            const income = input.hourlyRate * (duration / 60)
            input.income = Math.round(income * 100) / 100
            input.incomeText = input.income.toString()
            // 不重新格式化时薪文本，保持用户输入的格式
            if (!input.hourlyRateText || !input._isEditingHourlyRate) {
              input.hourlyRateText = input.hourlyRate.toFixed(2)
            }
          }
          // 否则根据收入计算时薪（默认行为或最后编辑的是收入）
          else if (input.income > 0) {
            input.hourlyRate = parseFloat(((parseFloat(input.income) || 0) / (duration / 60)).toFixed(2))
            // 只有在用户不在编辑时薪时才重新格式化
            if (!input._isEditingHourlyRate) {
              input.hourlyRateText = input.hourlyRate.toFixed(2)
            }
            // 如果没有明确的更新来源，标记为收入更新
            if (!input._lastUpdatedBy) {
              input._lastUpdatedBy = 'income'
            }
          }
        }
        // 如果既没有收入也没有时薪，保持空值状态
        else {
          input.hourlyRate = 0
          // 不强制设置hourlyRateText，保持用户输入状态
          if (!input.hourlyRateText) {
            input.hourlyRateText = ''
          }
          input.income = 0
          // 不强制设置incomeText，保持用户输入状态
          if (!input.incomeText) {
            input.incomeText = ''
          }
        }
      } else if (input.type === 'rest') {
        // 休息时间段清空收入和时薪
        input.hourlyRate = 0
        input.hourlyRateText = ''
        input.income = 0
        input.incomeText = ''
        input._lastUpdatedBy = undefined
      }
    })

    this.setData({
      timeInputs
    })
  },

  // ==================== 摸鱼管理方法 ====================

  /**
   * 添加摸鱼记录
   */
  onAddFishing() {
    this.setData({
      showFishingEditor: true,
      fishingEditorMode: 'add',
      editingFishing: null
    })
  },

  /**
   * 编辑摸鱼记录
   */
  onEditFishing(e) {
    const fishingId = e.currentTarget.dataset.id
    const fishing = this.data.displayFishes.find(f => f.id === fishingId)

    if (!fishing) {
      wx.showToast({
        title: '摸鱼记录不存在',
        icon: 'error'
      })
      return
    }

    this.setData({
      showFishingEditor: true,
      fishingEditorMode: 'edit',
      editingFishing: fishing
    })
  },

  /**
   * 删除摸鱼记录
   */
  onDeleteFishing(e) {
    const fishingId = e.currentTarget.dataset.id
    const fishing = this.data.displayFishes.find(f => f.id === fishingId)

    if (!fishing) {
      wx.showToast({
        title: '摸鱼记录不存在',
        icon: 'error'
      })
      return
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除这条摸鱼记录吗？\n时间：${fishing.startTime} - ${fishing.endTime}`,
      success: (res) => {
        if (res.confirm) {
          this.deleteFishingRecord(fishingId)
        }
      }
    })
  },

  /**
   * 摸鱼编辑器保存事件
   */
  onFishingEditorSave(e) {
    const { mode, fishingData } = e.detail

    try {
      if (mode === 'add') {
        this.addFishingRecord(fishingData)
      } else if (mode === 'edit') {
        this.updateFishingRecord(fishingData.id, fishingData)
      }

      // 关闭编辑器
      this.setData({
        showFishingEditor: false,
        editingFishing: null
      })
    } catch (error) {
      console.error('保存摸鱼记录失败:', error)
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  /**
   * 摸鱼编辑器删除事件
   */
  onFishingEditorDelete(e) {
    const { fishingData } = e.detail

    try {
      // 删除摸鱼记录
      this.deleteFishingRecord(fishingData.id)

      // 关闭编辑器
      this.setData({
        showFishingEditor: false,
        editingFishing: null
      })

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('删除摸鱼记录失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  },

  /**
   * 摸鱼编辑器取消事件
   */
  onFishingEditorCancel() {
    this.setData({
      showFishingEditor: false,
      editingFishing: null
    })
  },

  /**
   * 摸鱼编辑器关闭事件
   */
  onFishingEditorClose() {
    this.setData({
      showFishingEditor: false,
      editingFishing: null
    })
  },

  /**
   * 添加摸鱼记录
   */
  addFishingRecord(fishingData) {
    try {
      const dataManager = getApp().getDataManager()
      dataManager.addFishing(this.data.currentWorkId, this.data.selectedDate, fishingData)

      // 重新加载数据
      this.loadSelectedDateData()

      wx.showToast({
        title: '添加成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('添加摸鱼记录失败:', error)
      throw error
    }
  },

  /**
   * 更新摸鱼记录
   */
  updateFishingRecord(fishingId, updateData) {
    try {
      const dataManager = getApp().getDataManager()
      dataManager.updateFishing(this.data.currentWorkId, this.data.selectedDate, fishingId, updateData)

      // 重新加载数据
      this.loadSelectedDateData()

      wx.showToast({
        title: '更新成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('更新摸鱼记录失败:', error)
      wx.showToast({
        title: '更新失败',
        icon: 'error'
      })
    }
  },

  /**
   * 删除摸鱼记录
   */
  deleteFishingRecord(fishingId) {
    try {
      const dataManager = getApp().getDataManager()
      dataManager.deleteFishing(this.data.currentWorkId, this.data.selectedDate, fishingId)

      // 重新加载数据
      this.loadSelectedDateData()

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      console.error('删除摸鱼记录失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  },



  // ==================== 收入调整相关方法 ====================

  /**
   * 加载选中日期的收入调整数据
   */
  loadSelectedDateAdjustmentData() {
    if (!this.data.selectedDate || !this.data.currentWorkId) {
      return null
    }

    try {
      console.log('开始加载收入调整数据，日期:', this.data.selectedDate)
      const adjustmentSummary = this.incomeAdjustmentService.getDayAdjustmentSummary(
        this.data.selectedDate,
        this.data.currentWorkId
      )
      console.log('获取到的收入调整汇总:', adjustmentSummary)

      // 验证数据结构
      if (!adjustmentSummary) {
        console.error('收入调整汇总为空')
        return null
      }

      // 为额外收入项目添加类型文本和确保数据格式
      if (adjustmentSummary.extraIncomeItems && Array.isArray(adjustmentSummary.extraIncomeItems)) {
        console.log('处理额外收入项目，数量:', adjustmentSummary.extraIncomeItems.length)
        adjustmentSummary.extraIncomeItems = adjustmentSummary.extraIncomeItems.map(item => ({
          ...item,
          amount: Number(item.amount) || 0,  // 确保 amount 是数字
          typeText: item.type || '未设置类型'  // 直接使用用户输入的类型
        }))
      } else {
        console.log('额外收入项目为空或不是数组:', adjustmentSummary.extraIncomeItems)
        adjustmentSummary.extraIncomeItems = []
      }

      // 为扣款项目添加类型文本和确保数据格式
      if (adjustmentSummary.deductionItems && Array.isArray(adjustmentSummary.deductionItems)) {
        console.log('处理扣款项目，数量:', adjustmentSummary.deductionItems.length)
        adjustmentSummary.deductionItems = adjustmentSummary.deductionItems.map(item => ({
          ...item,
          amount: Number(item.amount) || 0,  // 确保 amount 是数字
          typeText: item.type || '未设置类型'  // 直接使用用户输入的类型
        }))
      } else {
        console.log('扣款项目为空或不是数组:', adjustmentSummary.deductionItems)
        adjustmentSummary.deductionItems = []
      }

      console.log('处理后的收入调整数据:', adjustmentSummary)

      // 调试：检查描述字段
      if (adjustmentSummary.extraIncomeItems && adjustmentSummary.extraIncomeItems.length > 0) {
        console.log('额外收入项目详情:', adjustmentSummary.extraIncomeItems.map(item => ({
          type: item.type,
          desc: item.desc,
          typeText: item.typeText,
          amount: item.amount
        })))
      }

      if (adjustmentSummary.deductionItems && adjustmentSummary.deductionItems.length > 0) {
        console.log('扣款项目详情:', adjustmentSummary.deductionItems.map(item => ({
          type: item.type,
          desc: item.desc,
          typeText: item.typeText,
          amount: item.amount
        })))
      }

      return adjustmentSummary

    } catch (error) {
      console.error('加载收入调整数据失败:', error)
      return null
    }
  },

  /**
   * 将Date对象转换为YYYY-MM-DD格式的字符串
   */
  formatDateString(date) {
    if (!date || !(date instanceof Date)) {
      return ''
    }

    return formatDateKey(date)
  },

  /**
   * 添加额外收入
   */
  onAddExtraIncome() {
    const selectedDate = this.data.selectedDate
    console.log('用户点击添加额外收入，当前选中日期:', selectedDate)

    // 确保有选中的日期
    if (!selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'error'
      })
      return
    }

    // 转换为日期字符串
    const dateString = this.formatDateString(selectedDate)
    console.log('转换后的日期字符串:', dateString)

    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'income',
      adjustmentModalEditItemId: '',
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 添加扣款
   */
  onAddDeduction() {
    const selectedDate = this.data.selectedDate
    console.log('用户点击添加扣款，当前选中日期:', selectedDate)

    // 确保有选中的日期
    if (!selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'error'
      })
      return
    }

    // 转换为日期字符串
    const dateString = this.formatDateString(selectedDate)
    console.log('转换后的日期字符串:', dateString)

    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'deduction',
      adjustmentModalEditItemId: '',
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 编辑额外收入
   */
  onEditExtraIncome(e) {
    const item = e.currentTarget.dataset.item
    const selectedDate = this.data.selectedDate
    console.log('用户点击编辑额外收入:', item, '选中日期:', selectedDate)

    if (!selectedDate) {
      wx.showToast({
        title: '日期错误',
        icon: 'error'
      })
      return
    }

    const dateString = this.formatDateString(selectedDate)
    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'income',
      adjustmentModalEditItemId: item.id,
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 编辑扣款
   */
  onEditDeduction(e) {
    const item = e.currentTarget.dataset.item
    const selectedDate = this.data.selectedDate
    console.log('用户点击编辑扣款:', item, '选中日期:', selectedDate)

    if (!selectedDate) {
      wx.showToast({
        title: '日期错误',
        icon: 'error'
      })
      return
    }

    const dateString = this.formatDateString(selectedDate)
    if (!dateString) {
      wx.showToast({
        title: '日期格式错误',
        icon: 'error'
      })
      return
    }

    this.setData({
      showIncomeAdjustmentModal: true,
      adjustmentModalMode: 'deduction',
      adjustmentModalEditItemId: item.id,
      adjustmentModalDateString: dateString
    })
  },

  /**
   * 收入调整数据更新事件
   */
  onIncomeAdjustmentDataUpdated(e) {
    const { mode, dateString, isEdit, itemId } = e.detail
    console.log('页面：收到收入调整数据更新通知', { mode, dateString, isEdit, itemId })

    // 重新加载选中日期的数据
    this.loadSelectedDateData()

    // 更新统计信息
    this.updateStatistics()

    // 触发全局数据更新事件
    this.triggerDataUpdateEvent()

    // 关闭模态框
    this.onIncomeAdjustmentModalClose()
  },

  /**
   * 关闭收入调整模态框
   */
  onIncomeAdjustmentModalClose() {
    this.setData({
      showIncomeAdjustmentModal: false,
      adjustmentModalEditItemId: '',
      adjustmentModalDateString: ''
    })
  },

  /**
   * 删除额外收入
   */
  onDeleteExtraIncome(e) {
    const itemId = e.currentTarget.dataset.id
    console.log('删除额外收入, ID:', itemId)

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条额外收入记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            this.incomeAdjustmentService.removeExtraIncome(
              this.data.selectedDate,
              itemId,
              this.data.currentWorkId
            )

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })

            // 重新加载数据
            this.loadSelectedDateData()
            this.updateStatistics()
            this.triggerDataUpdateEvent()

          } catch (error) {
            console.error('删除额外收入失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  },

  /**
   * 删除扣款
   */
  onDeleteDeduction(e) {
    const itemId = e.currentTarget.dataset.id
    console.log('删除扣款, ID:', itemId)

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条扣款记录吗？',
      success: (res) => {
        if (res.confirm) {
          try {
            this.incomeAdjustmentService.removeDeduction(
              this.data.selectedDate,
              itemId,
              this.data.currentWorkId
            )

            wx.showToast({
              title: '删除成功',
              icon: 'success'
            })

            // 重新加载数据
            this.loadSelectedDateData()
            this.updateStatistics()
            this.triggerDataUpdateEvent()

          } catch (error) {
            console.error('删除扣款失败:', error)
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            })
          }
        }
      }
    })
  }
})