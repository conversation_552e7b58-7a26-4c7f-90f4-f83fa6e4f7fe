# 智能填写收入组件精度问题修复

## 问题描述

在智能填写收入组件中发现了两个主要的精度问题：

1. **浮点数精度问题**：计算结果出现类似 `32.199999999` 的数值
2. **累积误差问题**：总收入100元，工作3小时，计算后总收入变成 `99.99`

## 问题原因

### 1. 浮点数精度问题
JavaScript 中的浮点数运算存在精度问题，例如：
```javascript
0.1 + 0.2 = 0.30000000000000004
1/3 * 3 = 0.9999999999999999
```

### 2. 累积误差问题
原有的分配算法中，每个时间段都按比例计算收入：
```javascript
input.income = parseFloat((totalAmount * ratio).toFixed(2))
```
这种方式会导致累积误差，使得总收入不等于各时间段收入之和。

## 修复方案

### 1. 添加精度处理方法
```javascript
_fixFloatPrecision(num, decimals = 2) {
  return Math.round(num * Math.pow(10, decimals)) / Math.pow(10, decimals)
}
```

### 2. 改进分配算法
采用"累积误差修正算法"：
- 前 n-1 个时间段按比例分配收入
- 最后一个时间段的收入 = 总收入 - 前面所有时间段收入之和

这样确保总收入精确等于各时间段收入之和。

## 修复内容

### 修复的方法

1. **`_calculateTotalIncomeDistribution`** - 总收入分配计算
2. **`_calculateOvertimeRateByHourly`** - 基础时薪方式计算加班倍率
3. **`_calculateOvertimeRateByTotal`** - 总收入方式计算加班倍率
4. **`_calculateHourlyRateIncome`** - 按分类时薪计算
5. **所有输入处理方法** - 确保用户输入也经过精度处理
6. **`fillDailyIncomeResult`** - 日收入计算器结果回填

### 修复效果

**修复前：**
```javascript
// 总收入100，3个1小时时间段
[
  { income: 33.33333333333333 },
  { income: 33.33333333333333 },
  { income: 33.33333333333333 }
]
// 总收入：99.99999999999999
```

**修复后：**
```javascript
// 总收入100，3个1小时时间段
[
  { income: 33.33 },
  { income: 33.33 },
  { income: 33.34 }  // 最后一个时间段补齐差额
]
// 总收入：100.00
```

## 技术细节

### 精度处理原理
使用 `Math.round()` 配合幂运算来修复精度：
```javascript
Math.round(num * 100) / 100  // 保留2位小数
```

### 累积误差修正
```javascript
// 前面的时间段按比例分配
if (index < workInputs.length - 1) {
  income = fixFloatPrecision(totalAmount * ratio)
  allocatedIncome += income
} else {
  // 最后一个时间段补齐差额
  income = fixFloatPrecision(totalAmount - allocatedIncome)
}
```

## 测试验证

通过测试验证了以下场景：
1. 单个时间段的精度处理
2. 多个不均匀时间段的分配
3. 容易产生精度问题的特殊数值
4. 用户反映的实际问题场景

所有测试均通过，确保：
- 不再出现 `32.199999999` 类似的精度问题
- 总收入始终等于各时间段收入之和
- 计算结果精确到小数点后2位

## 影响范围

此修复影响智能填写收入组件的所有计算模式：
- 总收入分配模式
- 加班倍率模式（基础时薪方式）
- 加班倍率模式（总收入方式）
- 分类时薪模式

修复后，用户在使用智能填写收入功能时将不再遇到精度问题。
