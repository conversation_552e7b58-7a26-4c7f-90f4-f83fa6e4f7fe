# 优化后数据管理使用指南

## 概述

本指南介绍如何使用优化后的数据管理架构。新架构采用清洁设计，移除了64.61%的冗余数据，实现了显著的性能提升和存储优化。

### 核心优化特点
- **数据结构最小化**：只存储核心字段，动态计算显示数据
- **存储空间优化**：超过70%的空间节省
- **ID简化**：从复杂长ID优化为简单递增数字
- **无历史包袱**：清洁架构，专注核心功能

## 快速开始

### 1. 导入数据管理器

```javascript
import dataManager from './utils/data-manager.js'
```

### 2. 确保数据已加载

```javascript
// 在页面onLoad或组件ready中
async onLoad() {
  await dataManager.ensureLoaded()
  // 现在可以安全地使用数据管理器
}
```

## 核心功能使用

### 工作履历管理

#### 添加工作履历
```javascript
const workData = {
  company: '新公司',
  position: '开发工程师',
  startDate: new Date('2024-01-01'),
  probationEndDate: new Date('2024-03-01'),
  formalSalary: 10000,
  probationSalary: 8000,
  notes: '备注信息',
  payDays: [
    {
      day: 15,
      name: '月中发薪'
    },
    {
      day: 30,
      name: '月末发薪'
    }
  ]
}

try {
  const workId = dataManager.addWork(workData)
  console.log('工作履历添加成功:', workId)
} catch (error) {
  console.error('添加失败:', error.message)
}
```

#### 获取工作履历
```javascript
// 获取所有工作履历
const allWorks = dataManager.getWorkHistory()

// 获取指定工作履历
const work = dataManager.getWork(workId)

// 获取当前工作履历（带缓存优化）
const currentWork = dataManager.getCurrentWork()
```

#### 更新工作履历
```javascript
try {
  dataManager.updateWork(workId, {
    formalSalary: 12000,
    notes: '更新后的备注'
  })
  console.log('更新成功')
} catch (error) {
  console.error('更新失败:', error.message)
}
```

#### 删除工作履历
```javascript
try {
  dataManager.deleteWork(workId)
  console.log('删除成功')
} catch (error) {
  console.error('删除失败:', error.message)
}
```

#### 发薪日管理
发薪日功能允许为每个工作履历设置多个发薪日期，支持每月第几号和倒数第几天两种模式，系统会自动计算距离下一个发薪日的天数。

##### 设置发薪日
```javascript
// 为工作履历设置发薪日（支持正数和负数）
const payDays = [
  {
    day: 15,        // 每月15号（正数表示第几号）
    name: '月中发薪'  // 发薪日名称
  },
  {
    day: -1,        // 每月倒数第1天（负数表示倒数第几天）
    name: '月末发薪'
  },
  {
    day: -3,        // 每月倒数第3天
    name: '月末前发薪'
  }
]

try {
  dataManager.updateWork(workId, { payDays: payDays })
  console.log('发薪日设置成功')
} catch (error) {
  console.error('设置失败:', error.message)
}
```

##### 获取发薪日信息
```javascript
// 获取工作履历的发薪日设置
const work = dataManager.getWork(workId)
console.log('发薪日设置:', work.payDays)

// 使用WorkHistoryService计算下一个发薪日
const { WorkHistoryService } = require('./core/services/work-history-service.js')
const workHistoryService = new WorkHistoryService()

const payDayInfo = workHistoryService.getNextPayDayInfo(workId)
console.log('下一个发薪日:', payDayInfo)
// 输出示例: {days: 5, payDayName: '月中发薪', nextPayDate: Date}
```

##### 发薪日显示规则
- **未设置发薪日**：显示"未设置" + "发薪日期"
- **今天是发薪日**：显示"今天" + 发薪日名称
- **未来发薪日**：显示"X天" + "距离" + 发薪日名称

##### 发薪日数据格式
```javascript
{
  day: 15,           // 必填：发薪日期
                     // 正数（1-31）：表示每月第几号
                     // 负数（-1到-31）：表示倒数第几天
  name: '月中发薪'    // 必填：发薪日名称，用于显示
}

// 示例：
// day: 15   -> 每月15号
// day: -1   -> 每月倒数第1天（月末最后一天）
// day: -3   -> 每月倒数第3天
```

##### 发薪日计算规则
- **正数天数**：直接使用指定日期，如果超过当月天数则使用当月最后一天
- **负数天数**：从月末倒数计算，-1表示最后一天，-2表示倒数第2天
- **自动适配**：自动处理不同月份的天数差异（如2月28/29天，4月30天等）
```

**注意事项：**
- 发薪日期支持正数（1-31号）和负数（倒数1-31天）
- 正数天数：如果某月没有指定日期（如2月30号），系统会自动调整到该月最后一天
- 负数天数：自动从月末倒数计算，适配不同月份的天数差异
- 可以设置多个发薪日，系统会自动选择最近的一个
- 发薪日名称会在仪表盘中显示，建议使用简洁明了的名称

### 设置管理

#### 获取和更新设置
```javascript
// 获取所有设置
const settings = dataManager.getSettings()

// 更新设置
dataManager.updateSettings({
  income: {
    decimalPlaces: 2,
    currencySymbol: '¥'
  },
  app: {
    theme: 'dark'
  }
})
```

#### 仪表盘设置

项目采用自主导航栏架构，每个仪表盘都有独特的导航栏和设置：

```javascript
// 获取当前仪表盘
const currentDashboard = dataManager.getCurrentDashboard()

// 设置仪表盘（通过共用切换器）
dataManager.setCurrentDashboard('dashboard2')

// 获取仪表盘配置
const config = dataManager.getDashboardConfig('dashboard1')

// 更新仪表盘配置（每个仪表盘独立管理）
dataManager.updateDashboardConfig('dashboard1', {
  showRealTimeIncome: false,
  chartHeight: 150
})
```

**仪表盘特性：**

| 仪表盘 | 导航栏主题 | 按钮布局 | 独立设置 | 切换方式 |
|--------|------------|----------|----------|----------|
| Dashboard1 | 蓝紫色渐变 | 左侧：切换+设置 | ✅ 经典仪表盘设置（含小数位数） | 共用切换器 |
| Dashboard2 | 橙黄色渐变 | 左侧：切换+设置 | ✅ 现代仪表盘设置（含小数位数） | 共用切换器 |

**切换仪表盘：**
1. 点击导航栏左侧的切换按钮（🔄）
2. 在弹出的切换器中选择目标仪表盘
3. 系统自动切换并保存选择

**修改设置：**
1. 点击导航栏左侧的设置按钮（⚙️）
2. 在对应仪表盘的设置模态框中调整选项：
   - 显示选项：控制各种信息的显示/隐藏
   - 界面参数：调整图表高度、进度条大小等
   - **收入小数位数**：设置该仪表盘的收入显示精度（0-3位，默认3位）
3. 点击保存按钮确认更改，设置立即生效

**注意事项：**
- 每个仪表盘的设置是独立的，可以设置不同的小数位数
- 设置保存后会自动重新加载数据以应用新的显示格式
- 点击模态框外部区域可关闭设置窗口，点击模态框内部不会关闭

### 时间段数据管理（优化后）

#### 创建时间段（存储格式）
```javascript
// 创建优化的时间段数据
const timeSegment = {
  id: 0,              // 简化ID（递增数字）
  start: 540,         // 开始时间（分钟数，540 = 09:00）
  end: 720,           // 结束时间（分钟数，720 = 12:00）
  type: "work",       // 时间段类型
  income: 150         // 收入金额
}

// 跨日时间段示例
const overtimeSegment = {
  id: 1,
  start: 1320,        // 22:00 (22 * 60 = 1320)
  end: 1560,          // 次日02:00 (26 * 60 = 1560)
  type: "overtime",
  income: 400
}
```

#### 数据格式转换
```javascript
import { timeSegmentService } from './core/services/time-segment-service.js'

// 存储格式转显示格式
const displaySegment = timeSegmentService.convertSegmentForDisplay(timeSegment)
// 结果：
// {
//   id: 0,
//   start: 540,
//   end: 720,
//   type: "work",
//   income: 150,
//   hourlyRate: 50,           // 动态计算：150 / 3小时 = 50元/小时
//   startTime: "09:00",       // 动态格式化
//   endTime: "12:00",         // 动态格式化
//   duration: "3小时",         // 动态计算
//   durationMinutes: 180,     // 动态计算
//   typeText: "工作"          // 动态映射
// }

// 清理数据用于存储
const cleanedData = timeSegmentService.cleanDayDataForStorage(dayData)
```

#### 时间格式转换
```javascript
import { minutesToTimeDisplay, timeStringToMinutes } from './utils/helpers/time-utils.js'

// 分钟数转时间显示
minutesToTimeDisplay(540)   // "09:00"
minutesToTimeDisplay(1560)  // "次日02:00"

// 时间字符串转分钟数
timeStringToMinutes("09:00", false)  // 540
timeStringToMinutes("02:00", true)   // 1560 (次日)

// 动态计算时薪
import { calculateHourlyRate } from './utils/helpers/time-utils.js'
const hourlyRate = calculateHourlyRate(timeSegment)  // 50
```

#### 设置日期计划
```javascript
const timeInputs = [
  {
    startTime: '09:00',
    endTime: '12:00',
    type: 'work',
    income: 150,
    isStartNextDay: false,
    isEndNextDay: false
  },
  {
    startTime: '22:00',
    endTime: '02:00',
    type: 'overtime',
    income: 400,
    isStartNextDay: false,
    isEndNextDay: true
  }
]

try {
  const result = timeSegmentService.setDaySchedule(
    new Date(),
    timeInputs,
    550,  // 总收入
    workId
  )
  console.log('设置成功:', result)
} catch (error) {
  console.error('设置失败:', error.message)
}
```

#### 获取和保存日期数据
```javascript
const today = new Date()
const workId = dataManager.getCurrentWork()?.id

if (workId) {
  // 获取今天的数据
  const dayData = dataManager.getDayData(workId, today)

  // 修改数据（只修改核心字段）
  dayData.status = 'active'
  // 注意：dailyIncome 现在通过 segments 动态计算，无需手动设置

  // 保存数据（自动清理冗余字段）
  dataManager.saveDayData(workId, today, dayData)
}
```

## 性能优化最佳实践

### 数据结构优化原则

#### 1. 最小化存储
```javascript
// ✅ 推荐：只存储核心数据
const segment = {
  id: 0,
  start: 540,
  end: 720,
  type: "work",
  income: 150
}

// ❌ 避免：存储可计算的冗余数据
const redundantSegment = {
  id: 0,
  start: 540,
  end: 720,
  type: "work",
  income: 150,
  hourlyRate: 50,        // 冗余：可计算
  startTime: "09:00",    // 冗余：可格式化
  endTime: "12:00",      // 冗余：可格式化
  duration: "3小时",      // 冗余：可计算
  createTime: "2025-07-18T17:14:33.893Z", // 冗余：无业务价值
  updateTime: "2025-07-18T17:14:33.893Z"  // 冗余：无业务价值
}
```

#### 2. 动态计算策略
```javascript
// 在需要显示时才计算
function getDisplayData(segments) {
  return segments.map(segment => ({
    ...segment,
    hourlyRate: calculateHourlyRate(segment),
    startTime: minutesToTimeDisplay(segment.start),
    endTime: minutesToTimeDisplay(segment.end),
    duration: formatDuration(segment.end - segment.start)
  }))
}

// 批量处理提高效率
function processSegmentsBatch(segments) {
  return segments.map((segment, index) => ({
    id: index,  // 重新分配连续ID
    start: segment.start,
    end: segment.end,
    type: segment.type,
    income: segment.income || 0
  }))
}
```

#### 3. 内存管理
```javascript
// 及时清理不需要的数据
function cleanupDisplayData(displaySegments) {
  // 只保留核心字段用于存储
  return displaySegments.map(segment => ({
    id: segment.id,
    start: segment.start,
    end: segment.end,
    type: segment.type,
    income: segment.income
  }))
}

// 使用对象池减少GC压力
const segmentPool = {
  pool: [],
  get() {
    return this.pool.pop() || {}
  },
  release(obj) {
    Object.keys(obj).forEach(key => delete obj[key])
    this.pool.push(obj)
  }
}
```

### 摸鱼功能管理

#### 开始和结束摸鱼

##### 开始摸鱼
```javascript
// 一键开始摸鱼
const result = dataManager.startFishing()

if (result.success) {
  console.log('摸鱼开始:', result.fishingState)
} else {
  console.error('开始失败:', result.message)
}
```

##### 结束摸鱼
```javascript
// 结束摸鱼并保存记录
const result = dataManager.endFishing((workId, date, fishingRecord) => {
  // 自动保存摸鱼记录
  dataManager.addFishing(workId, date, fishingRecord)
})

if (result.success) {
  console.log('摸鱼结束:', result.duration)
}
```

#### 摸鱼备注编辑

##### 随时编辑当前摸鱼备注
```javascript
// 更新当前摸鱼的备注
const result = dataManager.updateCurrentFishingRemark('喝水休息')

if (result.success) {
  console.log('备注更新成功:', result.fishingState.remark)
}
```

##### 获取当前摸鱼状态
```javascript
// 获取当前摸鱼状态
const fishingState = dataManager.getCurrentFishingState()

if (fishingState && fishingState.isActive) {
  console.log('当前摸鱼信息:', {
    开始时间: fishingState.startTime,
    备注: fishingState.remark || '无备注',
    工作时间段: fishingState.workSegment
  })
}
```

#### 摸鱼备注编辑器组件使用

##### 基本使用
```javascript
// 在组件中使用摸鱼备注编辑器
<fishing-remark-editor
  show="{{showRemarkEditor}}"
  current-remark="{{fishingState.remark}}"
  bind:save="onRemarkEditorSave"
  bind:cancel="onRemarkEditorClose"
  bind:close="onRemarkEditorClose">
</fishing-remark-editor>
```

##### 事件处理
```javascript
// 保存备注事件
onRemarkEditorSave(e) {
  const { remark } = e.detail

  // 更新摸鱼状态
  this.setData({
    'fishingState.remark': remark,
    showRemarkEditor: false
  })

  console.log('备注已保存:', remark)
}

// 取消/关闭事件
onRemarkEditorClose() {
  this.setData({
    showRemarkEditor: false
  })
}
```

#### 快捷备注功能

##### 历史备注统计
摸鱼备注编辑器基于用户的历史摸鱼记录，统计备注使用频次：

```javascript
// 快捷备注数据结构
const quickRemarks = [
  { remark: '喝水休息', count: 5 },  // 使用5次
  { remark: '上厕所', count: 3 },    // 使用3次
  { remark: '看新闻', count: 2 }     // 使用2次
]
```

##### 功能特性
- **智能排序**: 按使用频次从高到低排序
- **一键选择**: 点击快捷备注直接保存并关闭编辑器
- **智能过滤**: 过滤掉系统生成的备注（如"自动结束"）
- **精选显示**: 显示前10个最常用的备注

#### 摸鱼记录管理

##### 添加摸鱼记录
```javascript
// 手动添加摸鱼记录
const fishingData = {
  id: 0,
  start: 540,        // 9:00 (分钟数)
  end: 570,          // 9:30 (分钟数)
  remark: '喝水休息'
}

dataManager.addFishing(workId, new Date(), fishingData)
```

##### 默认显示处理
```javascript
// 在显示摸鱼记录时，空备注会显示为"日常摸鱼"
const displayRemark = fishingRecord.remark || '日常摸鱼'
```

#### 最佳实践

##### 1. 用户体验设计
- 开始摸鱼时无需填写备注，降低操作门槛
- 摸鱼过程中可随时编辑备注，提升灵活性
- 快捷备注提升输入效率

##### 2. 数据管理策略
- 备注数据保持简洁，空备注不占用存储空间
- 历史备注统计基于实际使用数据
- 智能过滤系统生成的备注

##### 3. 组件集成
```javascript
// 在fishing-control组件中集成编辑功能
onEditRemark() {
  if (!this.data.isFishing) return

  this.setData({
    showRemarkEditor: true
  })
}
```

### 存储优化技巧

#### 1. 自动数据压缩
应用使用Deflate算法自动压缩所有存储数据：

```javascript
// 数据自动压缩（无需手动处理）
dataManager.saveData()  // 自动使用Deflate压缩

// 压缩信息会在控制台显示：
// 开始Deflate压缩，原始大小: 6885 字符
// Deflate压缩后大小: 4231 字符
// 压缩率: 38.54%
```

**压缩特点：**
- 所有数据都进行压缩，无论大小
- 使用经典的Deflate算法（LZ77 + Huffman编码）
- 完全自动化，开发者无需关心
- 向后兼容未压缩数据

#### 2. 批量操作
```javascript
// 批量保存减少I/O操作
function saveBatchData(workId, dateDataMap) {
  const batchData = {}
  Object.entries(dateDataMap).forEach(([date, data]) => {
    batchData[date] = timeSegmentService.cleanDayDataForStorage(data)
  })
  return dataManager.saveBatchDayData(workId, batchData)
}
```

## 工具类使用

### 数据验证
```javascript
import { DataValidator } from './utils/data-utils.js'

// 验证日期
const isValid = DataValidator.isValidDate('2024-01-01')

// 验证正数
const isPositive = DataValidator.isPositiveNumber(100)

// 验证非空字符串
const isNonEmpty = DataValidator.isNonEmptyString('测试')
```

### 数据格式化
```javascript
import { DataFormatter } from './utils/data-utils.js'

// 格式化货币
const formatted = DataFormatter.formatCurrency(1234.56, {
  symbol: '¥',
  decimals: 2
}) // 输出: ¥1,234.56

// 格式化时长
const duration = DataFormatter.formatDuration(125) // 输出: 2小时5分钟

// 格式化日期
const dateStr = DataFormatter.formatDate(new Date(), 'YYYY-MM-DD HH:mm')
```

### 数据计算
```javascript
import { DataCalculator } from './utils/data-utils.js'

// 计算天数差
const days = DataCalculator.daysBetween('2024-01-01', '2024-01-15')

// 计算工作日
const workdays = DataCalculator.workdaysBetween('2024-01-01', '2024-01-15')

// 计算平均值
const avg = DataCalculator.average([10, 20, 30, 40])

// 计算百分比
const percent = DataCalculator.percentage(25, 100) // 输出: 25
```

### 数据安全
```javascript
import { DataSecurity } from './utils/data-utils.js'

// 脱敏手机号
const maskedPhone = DataSecurity.maskPhone('13812345678') // 输出: 138****5678

// 脱敏邮箱
const maskedEmail = DataSecurity.maskEmail('<EMAIL>') // 输出: t**<EMAIL>

// 脱敏金额
const maskedAmount = DataSecurity.maskAmount(1000) // 输出: ***
```

## 数据监听

### 添加数据变化监听器
```javascript
const listener = (userData) => {
  console.log('数据已更新:', userData.version)
  // 更新UI
  this.setData({
    currentWork: dataManager.getCurrentWork()
  })
}

// 添加监听器
dataManager.addChangeListener(listener)

// 移除监听器（在页面卸载时）
dataManager.removeChangeListener(listener)
```

## 性能优化

### 缓存机制
重构后的数据管理器内置了缓存机制，特别是对于频繁访问的数据：

- `getCurrentWork()` 方法使用了5秒缓存
- 数据变化时自动清理缓存
- 减少了重复计算和数据访问

### 防抖保存
数据保存使用了防抖机制，避免频繁写入存储：

```javascript
// 立即保存
dataManager.saveData(true)

// 防抖保存（默认1秒延迟）
dataManager.saveData()
```

## 错误处理

### 统一错误处理
所有管理器都提供了完善的错误处理：

```javascript
try {
  const workId = dataManager.addWork(workData)
} catch (error) {
  // 错误信息已经过验证和格式化
  wx.showToast({
    title: error.message,
    icon: 'error'
  })
}
```

### 数据验证
在数据操作前会进行严格的验证：

- 工作履历数据验证
- 日期格式验证
- 数值范围验证
- 必填字段检查

## 数据导入导出

### 导出数据
```javascript
try {
  const exportedData = dataManager.exportData()
  // 可以保存到文件或分享
  console.log('导出成功:', exportedData.length)
} catch (error) {
  console.error('导出失败:', error.message)
}
```

### 导入数据
```javascript
try {
  dataManager.importData(jsonString)
  console.log('导入成功')
} catch (error) {
  console.error('导入失败:', error.message)
}
```

## 测试和调试

### 运行测试
```javascript
import { runAllTests } from './utils/test-refactor.js'

// 在开发环境中运行测试
if (__DEV__) {
  runAllTests().then(success => {
    console.log('测试结果:', success ? '通过' : '失败')
  })
}
```

### 调试信息
数据管理器提供了详细的日志信息：

- 数据加载和保存日志
- Deflate压缩率统计和性能监控
- 压缩前后数据大小对比
- 性能计时
- 错误堆栈跟踪

**压缩日志示例：**
```
开始Deflate压缩，原始大小: 6885 字符
Deflate压缩后大小: 4231 字符
压缩率: 38.54%
数据保存成功
```

## 最佳实践

### 1. 数据访问
- 始终在使用前调用 `ensureLoaded()`
- 使用 `getCurrentWork()` 而不是直接访问数据
- 利用缓存机制，避免重复计算

### 2. 错误处理
- 使用 try-catch 包装所有数据操作
- 提供用户友好的错误提示
- 记录详细的错误日志

### 3. 性能优化
- 使用防抖保存避免频繁写入
- 合理使用数据监听器
- 及时移除不需要的监听器

### 4. 数据安全
- 使用工具类进行数据脱敏
- 验证所有用户输入
- 定期备份重要数据

## 新项目优势

作为全新项目，我们享有以下优势：

1. **纯净架构**: 零兼容性代码，专注核心功能
2. **极致性能**: 最小化存储，动态计算显示数据
3. **简洁设计**: 只保留必要字段，代码清晰易维护
4. **现代标准**: 采用最新的架构模式和最佳实践

## 总结

重构后的数据管理架构提供了：

- 更清晰的代码结构
- 更好的性能表现
- 更完善的错误处理
- 更丰富的工具方法
- 更友好的开发体验

## 摸鱼功能使用总结

### 功能亮点
1. **简化操作流程**: 一键开始摸鱼，随时编辑备注
2. **智能备注系统**: 基于历史数据的快捷备注推荐
3. **用户体验优化**: 友好的默认显示和视觉反馈
4. **数据管理优化**: 最小化存储，最大化功能

### 开发建议
- 使用组件化的备注编辑器提升代码复用性
- 利用快捷备注功能提升用户输入效率
- 保持数据结构简洁，在显示层处理默认值
- 通过调试日志排查数据结构兼容性问题

### 扩展性考虑
- 备注编辑器组件可复用于其他需要备注功能的场景
- 快捷备注算法可扩展到其他类型的历史数据统计
- 组件通信模式可作为其他功能开发的参考

通过遵循本指南，你可以充分利用项目架构的优势，包括摸鱼备注编辑功能，构建更稳定、更高效、更用户友好的应用。
